#!/usr/bin/env python3
"""
简单的GUI启动器 - 独立运行
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import time
import json
from datetime import datetime
import subprocess
import sys
import os


class SimpleCodeAlertGUI:
    """简化版的Code Alert AI Agent GUI"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.is_running = False
        self.server_process = None
        
        # 初始化界面
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        self.root.title("Code Alert AI Agent - 红警AI智能体")
        self.root.geometry("1000x700")
        self.root.configure(bg='#f0f0f0')
        
        # 主标题
        title_frame = tk.Frame(self.root, bg='#2c3e50', height=80)
        title_frame.pack(fill=tk.X)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, text="🎮 Code Alert AI Agent", 
                              font=('Arial', 20, 'bold'), 
                              fg='white', bg='#2c3e50')
        title_label.pack(expand=True)
        
        subtitle_label = tk.Label(title_frame, text="红警AI智能体控制界面 - Code Alert 黑客松", 
                                 font=('Arial', 12), 
                                 fg='#ecf0f1', bg='#2c3e50')
        subtitle_label.pack()
        
        # 主容器
        main_container = tk.Frame(self.root, bg='#f0f0f0')
        main_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 左侧控制面板
        left_frame = tk.LabelFrame(main_container, text="🎛️ 控制面板", 
                                  font=('Arial', 12, 'bold'), 
                                  bg='#f0f0f0', fg='#2c3e50')
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        left_frame.configure(width=300)
        
        # 右侧日志面板
        right_frame = tk.LabelFrame(main_container, text="📝 系统日志", 
                                   font=('Arial', 12, 'bold'), 
                                   bg='#f0f0f0', fg='#2c3e50')
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        self.create_control_panel(left_frame)
        self.create_log_panel(right_frame)
        
        # 状态栏
        self.create_status_bar()
        
        # 绑定关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
    def create_control_panel(self, parent):
        """创建控制面板"""
        # 服务器控制
        server_frame = tk.LabelFrame(parent, text="🚀 服务器控制", bg='#f0f0f0')
        server_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.start_button = tk.Button(server_frame, text="启动 AI Agent 服务器", 
                                     command=self.start_server,
                                     bg='#27ae60', fg='white', 
                                     font=('Arial', 10, 'bold'),
                                     height=2)
        self.start_button.pack(fill=tk.X, padx=5, pady=5)
        
        self.stop_button = tk.Button(server_frame, text="停止服务器", 
                                    command=self.stop_server,
                                    bg='#e74c3c', fg='white', 
                                    font=('Arial', 10, 'bold'),
                                    height=2, state=tk.DISABLED)
        self.stop_button.pack(fill=tk.X, padx=5, pady=5)
        
        # 状态显示
        status_frame = tk.LabelFrame(parent, text="📊 状态信息", bg='#f0f0f0')
        status_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.status_label = tk.Label(status_frame, text="状态: 未启动", 
                                    font=('Arial', 10, 'bold'),
                                    bg='#f0f0f0')
        self.status_label.pack(anchor=tk.W, padx=5, pady=2)
        
        self.port_label = tk.Label(status_frame, text="端口: 8000", 
                                  bg='#f0f0f0')
        self.port_label.pack(anchor=tk.W, padx=5, pady=2)
        
        # 快捷操作
        actions_frame = tk.LabelFrame(parent, text="⚡ 快捷操作", bg='#f0f0f0')
        actions_frame.pack(fill=tk.X, padx=10, pady=10)
        
        open_browser_btn = tk.Button(actions_frame, text="🌐 打开Web界面", 
                                    command=self.open_browser,
                                    bg='#3498db', fg='white')
        open_browser_btn.pack(fill=tk.X, padx=5, pady=2)
        
        test_api_btn = tk.Button(actions_frame, text="🧪 测试API", 
                                command=self.test_api,
                                bg='#9b59b6', fg='white')
        test_api_btn.pack(fill=tk.X, padx=5, pady=2)
        
        clear_log_btn = tk.Button(actions_frame, text="🗑️ 清空日志", 
                                 command=self.clear_log,
                                 bg='#95a5a6', fg='white')
        clear_log_btn.pack(fill=tk.X, padx=5, pady=2)
        
        # 项目信息
        info_frame = tk.LabelFrame(parent, text="ℹ️ 项目信息", bg='#f0f0f0')
        info_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        info_text = """🏆 Code Alert 黑客松大赛
🎮 基于OpenRA红警引擎
🧠 AI智能体自然语言控制
🚀 Python + FastAPI架构

📖 使用说明:
1. 点击"启动服务器"
2. 打开Web界面进行控制
3. 发送自然语言指令
4. 观察AI执行结果"""
        
        info_label = tk.Label(info_frame, text=info_text, 
                             justify=tk.LEFT, bg='#f0f0f0',
                             font=('Arial', 9))
        info_label.pack(anchor=tk.W, padx=5, pady=5)
        
    def create_log_panel(self, parent):
        """创建日志面板"""
        self.log_text = scrolledtext.ScrolledText(parent, wrap=tk.WORD, 
                                                 font=('Consolas', 9),
                                                 bg='#2c3e50', fg='#ecf0f1')
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 添加欢迎信息
        welcome_msg = """🎮 Code Alert AI Agent 控制界面
=" * 50
🏆 欢迎参加 Code Alert 黑客松大赛！
🚀 这是您的红警AI智能体控制中心

📋 操作步骤:
1. 点击"启动 AI Agent 服务器"启动后端服务
2. 点击"打开Web界面"访问控制界面  
3. 在Web界面中发送自然语言指令控制游戏
4. 观察AI智能体的执行结果和游戏状态

💡 提示: 确保已激活conda环境 (code-alert)
=" * 50

"""
        self.log_text.insert(tk.END, welcome_msg)
        
    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = tk.Frame(self.root, bg='#34495e', height=25)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        self.status_bar.pack_propagate(False)
        
        self.status_text = tk.Label(self.status_bar, text="就绪", 
                                   bg='#34495e', fg='white',
                                   anchor=tk.W)
        self.status_text.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=10)
        
        self.time_label = tk.Label(self.status_bar, text="", 
                                  bg='#34495e', fg='white')
        self.time_label.pack(side=tk.RIGHT, padx=10)
        
        # 更新时间
        self.update_time()
        
    def update_time(self):
        """更新时间显示"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.config(text=current_time)
        self.root.after(1000, self.update_time)
        
    def log_message(self, message: str, level: str = "INFO"):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        
    def start_server(self):
        """启动AI Agent服务器"""
        if self.is_running:
            self.log_message("服务器已在运行中", "WARNING")
            return
            
        try:
            self.log_message("正在启动AI Agent服务器...")
            
            # 启动FastAPI服务器
            cmd = [sys.executable, "-m", "src.main"]
            self.server_process = subprocess.Popen(cmd, 
                                                  stdout=subprocess.PIPE, 
                                                  stderr=subprocess.STDOUT,
                                                  universal_newlines=True,
                                                  bufsize=1)
            
            self.is_running = True
            self.start_button.config(state=tk.DISABLED)
            self.stop_button.config(state=tk.NORMAL)
            
            self.status_label.config(text="状态: 运行中")
            self.status_text.config(text="AI Agent服务器已启动")
            
            self.log_message("AI Agent服务器启动成功！")
            self.log_message("服务器地址: http://localhost:8000")
            self.log_message("API文档: http://localhost:8000/docs")
            
            # 启动日志监控线程
            threading.Thread(target=self.monitor_server_output, daemon=True).start()
            
        except Exception as e:
            self.log_message(f"启动服务器失败: {str(e)}", "ERROR")
            messagebox.showerror("错误", f"启动服务器失败:\n{str(e)}")
            
    def monitor_server_output(self):
        """监控服务器输出"""
        if not self.server_process:
            return
            
        try:
            for line in iter(self.server_process.stdout.readline, ''):
                if line:
                    self.log_message(f"服务器: {line.strip()}")
                if self.server_process.poll() is not None:
                    break
        except Exception as e:
            self.log_message(f"监控服务器输出错误: {str(e)}", "ERROR")
            
    def stop_server(self):
        """停止AI Agent服务器"""
        if not self.is_running:
            self.log_message("服务器未在运行", "WARNING")
            return
            
        try:
            self.log_message("正在停止AI Agent服务器...")
            
            if self.server_process:
                self.server_process.terminate()
                self.server_process.wait(timeout=5)
                self.server_process = None
                
            self.is_running = False
            self.start_button.config(state=tk.NORMAL)
            self.stop_button.config(state=tk.DISABLED)
            
            self.status_label.config(text="状态: 已停止")
            self.status_text.config(text="AI Agent服务器已停止")
            
            self.log_message("AI Agent服务器已停止")
            
        except Exception as e:
            self.log_message(f"停止服务器失败: {str(e)}", "ERROR")
            
    def open_browser(self):
        """打开浏览器"""
        import webbrowser
        url = "http://localhost:8000"
        webbrowser.open(url)
        self.log_message(f"已打开浏览器: {url}")
        
    def test_api(self):
        """测试API连接"""
        self.log_message("正在测试API连接...")
        
        try:
            import requests
            response = requests.get("http://localhost:8000/ping", timeout=5)
            if response.status_code == 200:
                self.log_message("API连接测试成功！")
                messagebox.showinfo("成功", "API连接正常")
            else:
                self.log_message(f"API测试失败: HTTP {response.status_code}", "ERROR")
        except requests.exceptions.ConnectionError:
            self.log_message("API连接失败: 服务器未启动", "ERROR")
            messagebox.showerror("错误", "无法连接到服务器\n请先启动AI Agent服务器")
        except Exception as e:
            self.log_message(f"API测试错误: {str(e)}", "ERROR")
            
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        self.log_message("日志已清空")
        
    def on_closing(self):
        """关闭程序时的处理"""
        if self.is_running:
            if messagebox.askokcancel("退出", "服务器正在运行，确定要退出吗？"):
                self.stop_server()
                self.root.destroy()
        else:
            self.root.destroy()
            
    def run(self):
        """运行GUI"""
        self.root.mainloop()


def main():
    """主函数"""
    try:
        print("🚀 启动 Code Alert AI Agent GUI...")
        app = SimpleCodeAlertGUI()
        app.run()
    except Exception as e:
        print(f"GUI启动失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
