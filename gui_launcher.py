#!/usr/bin/env python3
"""
Code Alert AI Agent GUI 启动器
用于启动图形用户界面
"""

import sys
import os
import logging

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def main():
    """主函数"""
    try:
        print("🚀 启动 Code Alert AI Agent GUI...")
        print("=" * 50)
        print("🎮 红警AI智能体控制界面")
        print("🏆 Code Alert 黑客松大赛")
        print("=" * 50)
        
        # 导入GUI模块
        from src.gui.main_window import CodeAlertGUI
        
        # 创建并运行GUI
        app = CodeAlertGUI()
        app.run()
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保已安装所有依赖包")
        print("运行: conda activate code-alert")
        sys.exit(1)
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
