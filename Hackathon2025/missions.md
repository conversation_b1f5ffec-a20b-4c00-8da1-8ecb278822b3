# 游戏关卡

### 比赛任务 (Missions)

开发一个 AI Agent，接收用户输入的自然语言指令，在 20 个关卡（暂定）中完成任务目标。每当完成一个关卡，游戏将生成一份加密日志，参赛者需上传该日志至官网用于评分，得分将实时公布在 [Leader boards]() 榜单。

### 关卡场景

游戏关卡设计的难度有易有难，涵盖多种挑战。不同的关卡反映不同的场景，包括但不限于：

- 生产建造类场景：

  - 基础建造训练 & 基础生产训练 

- 探索迷雾类场景：

  - 步兵探索（寻找敌方基地）

  - 飞机探索（开图）

- 战略撤退类场景：
- 战略防御类场景：
  - 修复建筑和单位

- 战略进攻类场景：
  - 两路夹击

- 战术指挥类场景：

  - 空中作战

  - 地面作战



#### 评分与日志上传

- 游戏自动生成包含关键指标（通关时间、击杀数、战损比等）的加密日志
- 参赛者将日志上传至官网（[link]）
- 组委会根据评估规则评分并更新排名
