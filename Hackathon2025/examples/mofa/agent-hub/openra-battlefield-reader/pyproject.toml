[tool.poetry]
name = "openra-battlefield-reader"
version = "0.1.1"
authors = [
    "<PERSON>",
]
description = "A simple openra-battlefield-reader template"
license = "MIT License"
homepage = "https://github.com/moxin-org/mofa"
documentation = "https://github.com/moxin-org/mofa/blob/main/README.md"
readme = "README.md"
packages = [{ include = "openra_battlefield_reader" }]

[tool.poetry.dependencies]
pyarrow = ">= 5.0.0"
flask = ">= 2.3.3"
flask-socketio = ">= 5.3.6"
python-socketio = ">= 5.8.0"
openai = ">= 1.50.0"
requests = ">= 2.31.0"

[tool.poetry.scripts]
openra-battlefield-reader = "openra_battlefield_reader.main:main"

[build-system]
requires = ["poetry-core>=1.8.0"]
build-backend = "poetry.core.masonry.api"

