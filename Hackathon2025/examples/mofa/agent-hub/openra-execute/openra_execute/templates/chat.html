<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OpenRA AI 对话控制</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .chat-container {
            width: 800px;
            height: 600px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-header {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 20px;
            font-weight: bold;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 15px;
            padding: 12px 16px;
            border-radius: 12px;
            max-width: 80%;
            word-wrap: break-word;
            white-space: pre-wrap;
            animation: fadeIn 0.3s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .user-message {
            background: #007bff;
            color: white;
            margin-left: auto;
            text-align: right;
        }

        .ai-message {
            background: white;
            border: 2px solid #e9ecef;
            color: #333;
        }

        .ai-message.system {
            background: #e3f2fd;
            border-color: #2196f3;
            color: #1976d2;
        }

        .ai-message.info {
            background: #f3e5f5;
            border-color: #9c27b0;
            color: #7b1fa2;
        }

        .ai-message.success {
            background: #e8f5e8;
            border-color: #4caf50;
            color: #388e3c;
        }

        .ai-message.error {
            background: #ffebee;
            border-color: #f44336;
            color: #d32f2f;
        }

        .ai-message.warning {
            background: #fff3e0;
            border-color: #ff9800;
            color: #f57c00;
        }

        .ai-message.process {
            background: #f0f4c3;
            border-color: #cddc39;
            color: #689f38;
        }

        .chat-input-container {
            padding: 20px;
            background: white;
            border-top: 1px solid #e9ecef;
            display: flex;
            gap: 10px;
        }

        .chat-input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s;
        }

        .chat-input:focus {
            border-color: #007bff;
        }

        .send-button {
            padding: 12px 24px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: background 0.3s;
        }

        .send-button:hover:not(:disabled) {
            background: #0056b3;
        }

        .send-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }

        .status-indicator {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #28a745;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .disconnected {
            background: #dc3545;
        }

        .example-commands {
            background: #f8f9fa;
            padding: 10px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #007bff;
            font-size: 14px;
            color: #666;
        }

        .example-commands h4 {
            color: #007bff;
            margin-bottom: 8px;
        }

        .example-commands ul {
            list-style: none;
            padding-left: 0;
        }

        .example-commands li {
            padding: 2px 0;
            cursor: pointer;
            transition: color 0.3s;
        }

        .example-commands li:hover {
            color: #007bff;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            🤖 OpenRA AI 对话控制系统
            <div class="status-indicator" id="statusIndicator"></div>
        </div>
        
        <div class="chat-messages" id="messages">
            <div class="example-commands">
                <h4>💡 示例指令：</h4>
                <ul>
                    <li onclick="insertCommand(this)">多多生产战车！</li>
                    <li onclick="insertCommand(this)">专注防御建设</li>
                    <li onclick="insertCommand(this)">疯狂造兵</li>
                    <li onclick="insertCommand(this)">攻击敌人基地</li>
                    <li onclick="insertCommand(this)">建造更多发电站</li>
                </ul>
            </div>
        </div>
        
        <div class="chat-input-container">
            <input type="text" id="messageInput" class="chat-input" 
                   placeholder="输入你的游戏指令...比如：多生产战车！" 
                   maxlength="200">
            <button id="sendButton" class="send-button" onclick="sendMessage()">
                发送
            </button>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
    <script>
        // Socket.IO连接
        const socket = io();
        const messagesDiv = document.getElementById('messages');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const statusIndicator = document.getElementById('statusIndicator');

        // 连接状态管理
        socket.on('connect', function() {
            console.log('连接成功');
            statusIndicator.classList.remove('disconnected');
            addSystemMessage('🔗 已连接到OpenRA AI系统');
        });

        socket.on('disconnect', function() {
            console.log('连接断开');
            statusIndicator.classList.add('disconnected');
            addSystemMessage('⚠️ 连接已断开');
        });

        // 接收AI消息
        socket.on('ai_message', function(data) {
            addAIMessage(data.message, data.type || 'default');
        });

        // 回显用户消息
        socket.on('user_message_echo', function(data) {
            addUserMessage(data.message);
        });

        // 发送消息
        function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;

            socket.emit('user_message', {message: message});
            messageInput.value = '';
            messageInput.focus();
        }

        // 插入示例命令
        function insertCommand(element) {
            messageInput.value = element.textContent;
            messageInput.focus();
        }

        // 添加用户消息
        function addUserMessage(message) {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message user-message';
            messageDiv.textContent = `👤 ${message}`;
            messagesDiv.appendChild(messageDiv);
            scrollToBottom();
        }

        // 添加AI消息
        function addAIMessage(message, type = 'default') {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ai-message ${type}`;
            messageDiv.textContent = message;
            messagesDiv.appendChild(messageDiv);
            scrollToBottom();
        }

        // 添加系统消息
        function addSystemMessage(message) {
            addAIMessage(message, 'system');
        }

        // 滚动到底部
        function scrollToBottom() {
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        // 回车发送
        messageInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // 初始化焦点
        window.onload = function() {
            messageInput.focus();
        };

        // 防止多次快速点击
        let isProcessing = false;
        socket.on('ai_message', function(data) {
            if (data.type === 'process') {
                isProcessing = true;
                sendButton.disabled = true;
                sendButton.innerHTML = '<div class="loading"></div>';
            } else if (data.type === 'success' || data.type === 'error' || data.type === 'system') {
                isProcessing = false;
                sendButton.disabled = false;
                sendButton.innerHTML = '发送';
            }
            addAIMessage(data.message, data.type || 'default');
        });
    </script>
</body>
</html>