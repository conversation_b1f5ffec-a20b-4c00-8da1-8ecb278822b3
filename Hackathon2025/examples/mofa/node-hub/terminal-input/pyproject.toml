[tool.poetry]
name = "terminal-input"
version = "0.3.6"
authors = [
    "<PERSON><PERSON><PERSON> <<EMAIL>>",
    "<PERSON><PERSON> <<EMAIL>>",
]
description = "Dora terminal input"
license = "MIT License"
homepage = "https://github.com/dora-rs/dora.git"
documentation = "https://github.com/dora-rs/dora/blob/main/node-hub/terminal-input/README.md"
readme = "README.md"
packages = [{ include = "terminal_input" }]

[tool.poetry.dependencies]
numpy = "< 2.0.0"
pyarrow = ">= 5.0.0"

[tool.poetry.scripts]
terminal-input = "terminal_input.main:main"

[build-system]
requires = ["poetry-core>=1.8.0"]
build-backend = "poetry.core.masonry.api"

#[project]
#readme = "README.md"
