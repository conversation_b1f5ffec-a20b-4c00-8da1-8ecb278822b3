[tool.poetry]
name = "openai-server-stream"
version = "0.3.6"
authors = [
    "<PERSON><PERSON><PERSON> <<EMAIL>>",
]
description = "Dora OpenAI API Server"
license = "MIT License"
homepage = "https://github.com/dora-rs/dora.git"
documentation = "https://github.com/dora-rs/dora/blob/main/node-hub/openai-server-stream/README.md"
readme = "README.md"
packages = [{ include = "openai_server_stream" }]

[tool.poetry.dependencies]
dora-rs = "^0.3.6"
numpy = "< 2.0.0"
pyarrow = ">= 5.0.0"
python = "^3.7"
fastapi = "^0.115"
asyncio = "^3.4"
uvicorn = "^0.31"
pydantic = "^2.9"

[tool.poetry.scripts]
openai-server-stream = "openai_server_stream.main:main"

[build-system]
requires = ["poetry-core>=1.8.0"]
build-backend = "poetry.core.masonry.api"
