[tool.poetry]
name = "multiple-terminal-input"
version = "0.1.1"
authors = [
    "<PERSON><PERSON><PERSON><PERSON>",
    "<PERSON>",
]
description = "Dora multiple-terminal-input"
license = "MIT License"
homepage = "https://github.com/dora-rs/dora.git"
documentation = "https://github.com/dora-rs/dora/blob/main/node-hub/terminal-input/README.md"
readme = "README.md"
packages = [{ include = "multiple_terminal_input" }]

[tool.poetry.dependencies]
numpy = "< 2.0.0"
pyarrow = ">= 5.0.0"

[tool.poetry.scripts]
multiple-terminal-input = "multiple_terminal_input.main:main"

[build-system]
requires = ["poetry-core>=1.8.0"]
build-backend = "poetry.core.masonry.api"

[project]
readme = "README.md"
