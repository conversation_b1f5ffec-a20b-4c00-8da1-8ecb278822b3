# Code Alert

<center>A 2025 GOSIM Hackathon</center>

## You Command, AI Conquers



| 比赛阶段     | 时间                                                         |
| ------------ | ------------------------------------------------------------ |
| 初赛：       | 2025年8月4日-9月1日，线上比赛（[报名网站](https://hackathon.scimigo.com/)） |
| 决赛嘉年华： | 2025年9月13日-9月14日，中国 杭州 [GOSIM AI 大会](https://hangzhou2025.gosim.org) |

**红色警戒：AI 崛起！**《红色警戒》（Red Alert）是一款划时代的即时战略（RTS）游戏，由 Westwood Studios 于 1996 年推出，是经典《命令与征服》（Command & Conquer）系列的重要代表作。游戏构建在架空的冷战背景下，玩家可选择扮演盟军或苏联，通过建设基地、采集资源、调配军队进行激烈对抗。其深度的战术系统与紧凑的游戏节奏在当年引发轰动，至今仍被无数玩家所津津乐道。

对老玩家来说，《红色警戒》不仅是一款锻炼战术与协作的游戏，更承载着一段青春记忆；对新玩家而言，虽然画面与操作显得复古，但其独特的魅力与挑战性依旧。就这样，《红警》吸引着一代又一代的玩家。

那么，如果把AI引入这款经典游戏会怎样？如果让AI成为你的副官，由你下达命令，AI来征战沙场——**You command, AI conquers**，会不会打开一扇全新的大门？

Red Alert: AI Uprising，红警之AI崛起！我们邀你来共同探索！加入《Code Alert》 AI红警黑客松，开发一个能理解玩家自然语言指令并转化为游戏操作的智能体（AI Agent）。你就是指挥官，AI就是副官——听你号令，指哪打哪！

------

## 游戏引擎与开发接口

本次大赛基于开源项目 **[OpenRA](https://github.com/OpenRA/OpenRA)**，它致力于重构与现代化经典的红警游戏，引擎支持 Windows、macOS 和 Linux 平台，并在玩法上引入诸多现代机制。目前 OpenRA 在 GitHub 上已有超过 15,700 Stars（截至 2025年7月）。

在 OpenRA 基础上，黑客松组委会简化了游戏，保留核心单位，并开发了具备开放接口的游戏引擎 ([GitHub代码仓](https://github.com/OpenCodeAlert/Hackathon2025))，支持通过 [Socket API](https://github.com/OpenCodeAlert/Hackathon2025/blob/main/APIs/socket-apis.md)控制游戏，参赛者可自由选择擅长的语言与接口，可以进一步封装成C#、Python、RESTful API，MCP 工具等，构建属于自己的 AI Agent 。

------

## 报名方式

报名入口：[https://hackathon.scimigo.com/](https://hackathon.scimigo.com/)
支持个人参赛，也支持组队（由队长报名并提交队员信息）。

------

## 比赛阶段

| 阶段 | 地点             | 时间              | 说明                                                         |
| ---- | ---------------- | ----------------- | ------------------------------------------------------------ |
| 初赛 | 线上进行         | 8月4日 - 9月1日  | 参赛者组队并下载游戏引擎，通过自然语言指令操控AI智能体通关官方提供的关卡。结果上传至大赛官网评估。 |
| 决赛 | 杭州 · GOSIM大会 | 9月13日 - 9月14日 | 邀请初赛表现优异的队伍到场参赛，进行现场对战、展示及AI红警世界杯总决赛。 |

## 初赛

初赛的基本方式是由参赛者下载游戏引擎和关卡到本地，开发大语言模型驱动的Agent，执行游戏API，完成关卡的方式进行。

- 初赛的时间是8月4日至9月1日。

- 初赛关卡由黑客松组织者通过GitHub代码仓提供。
  

  【注意】：关卡会在GitHub分期上线，因此，选手可能需要更新本地下载的软件。组委会预计提供20个左右关卡。

- 当选手的AI Agent完成某关卡，游戏引擎将生成该关卡的Log文件。选手需将Log文件上传官网。Log文件将作为比赛评分的主要依据。

- 更加详细的信息，请参考开发者指南文档。


【注意】：基于开发者的自我修养，**严禁更改log文件**。否则，组委会有权取消开发者的比赛资格。

### 比赛资源获取

- [访问比赛官网](https://hackathon.scimigo.com/)：官网提供报名信息、日程安排、规则说明、FAQ 等。

- [下载定制版红警游戏引擎和关卡软件包](https://github.com/OpenCodeAlert/Hackathon2025/releases)：由于新的比赛关卡会在比赛期间的不断发布，以及可能的bug修改问题，选手可能需要及时更新游戏引擎的下载。

- [获取开发接口文档](https://github.com/OpenCodeAlert/Hackathon2025/guideline.md)

- 示例 Agents：组委会邀请了两组开发者，用不同的Agent技术开发了简单的示例Agents供大家参考。

  - [MCP Agents](https://github.com/OpenCodeAlert/Hackathon2025/tree/main/examples/mcp)
  - [MoFA Agents](https://github.com/OpenCodeAlert/Hackathon2025/tree/main/examples/mofa)


  【注意】示例Agents仅提供了非常有限的实现。其唯一目的就是提供参赛者参考，方便参赛者启动自己的项目。组委会无意推荐参赛者这些示例的实现方式。

-  [了解关卡的情况](missions/)。关卡与游戏引擎在同一软件包中提供。在比赛过程中，当官方有新的关卡提供，选手需要[更新软件包](https://github.com/OpenCodeAlert/Hackathon2025/releases)。

### 指令输入方式

初赛阶段的玩家指令将通过文本方式传递给 Agent。
欢迎尝试语音输入（非必选）。杭州决赛将采用语音输入的方式。

### 评分与日志上传

- 游戏自动生成包含关键指标（通关时间、击杀数、战损比等）的加密日志
- 参赛者将日志上传至[官网][https://hackathon.scimigo.com/leaderboard/]
- 组委会根据评估规则评分并更新[排名](https://hackathon.scimigo.com/leaderboard/)

## 决赛(嘉年华)

决赛将于 9月13日-14日 在 [GOSIM China 大会](https://hangzhou2025.gosim.org)现场举办。这不仅仅是是进一步的技术比拼，我们更希望将它打造为红警玩家、AI开发者和开源社区的盛事（嘉年华）。

- 初赛前六名的团队将受邀参加决赛（嘉年华）阶段的比赛。

- 组委会将承担每队一个选手的交通和住宿费用。

- 如前六名团队中有团队不能参加决赛阶段的比赛，将依名次从前到后的原则递补。

  

#### AI红警“世界杯”

决赛比赛原则上采取 **2队捉对厮杀、单循环制**。胜率最高队伍夺冠，如胜率相同则进行加赛。

- 与初赛文本输入的方式不同，参赛者将通过**语音输入**指令。这要求参加决赛的团队在9月1日到9月12日之间开发相应功能。
- 组委会提供本地部署的 Moxin-7B 模型，实现语音转文字支持
- 与初赛的参赛者指挥Agent打关卡的方式不同，决赛的参赛者将指挥各自的AI Agent 作战与“敌方“参赛者指挥的AI Agent进行作战。
- 比赛将在大屏幕进行现场展示，并通过网络进行直播
- GOSIM大会期间，还将安排观众挑战赛，邀请红警资深玩家与AI参赛队同台竞技等嘉年华活动。

## 比赛规则

- 初赛阶段将依据选手在排行榜上的总分进行排名。
- 排行榜总分为选手在各个关卡中获得的得分之和。
- 每个关卡的得分规则将另行公布。

- 组委会将邀请初赛排名前六的选手参加在 GOSIM 大会现场举行的决赛对战环节。
   决赛阶段的交通及住宿费用由组委会承担。
   选手的最终名次将根据现场对战的胜率确定。
- 奖项设置如下：一等奖 1 名，二等奖 1 名，三等奖 3 名。
- 获奖选手将在 GOSIM 大会颁奖环节中接受表彰。

【注意】
为确保比赛公平公正、顺利进行，主办方保留在特殊情况下调整部分规则的权利，任何修改将第一时间公开并解释其合理性。

## 奖励设置

- **冠军**：一名，获奖证书和一台5090的笔记本电脑。
- **亚军**：一名，获奖证书和macbook  pro笔记本电脑
- **季军**：三名，获奖证书和mac mini 电脑
- 组委会将邀请初赛排名前六的选手参加在 GOSIM 大会现场举行的决赛对战，并承担交通和住宿费用。
- 获奖选手将在 GOSIM 大会颁奖环节中接受表彰。
- 获奖队伍及优秀作品将在官网与社交媒体平台展示并推广

## 免责申明

感谢大家支持对黑客松的支持和参与。这次活动是一次尝试和探索的活动，组委会深知有很多地方都会不够成熟，比如在评分规则、关卡设计、比赛流程设计等方面，都毫无意外地会出现不尽人意之处。组委会希望得到大家的谅解。希望不仅仅将这次活动视为一次竞赛，更将其视为一次开源社区，游戏社区和智能体开发社区的嘉年华，让我们一起来享受这个前所未有的过程。

同时，组委会一定会认真接受大家的意见建议，尽可能地改进这次比赛和未来比赛的开发者体验！

欢迎每一位热爱红警、热心AI的你，加入这场命令与征服的新战役！

**You Command, AI Conquers! 你来指挥，AI来战！** 
