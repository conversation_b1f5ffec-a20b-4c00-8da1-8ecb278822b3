import os
import subprocess
import tkinter as tk
from tkinter import messagebox, Tk, ttk, StringVar
import psutil
import shutil
import requests
import socket
import configparser
import sys
import zipfile
import threading
import time
import pygetwindow as gw
import ctypes

CONFIG_FILE = "settings.ini"
VERSION = "0.4.0"


def load_settings():
    config = configparser.ConfigParser()
    if os.path.exists(CONFIG_FILE):
        config.read(CONFIG_FILE)
        if "Settings" in config:
            openai_key_entry.insert(0, config.get(
                "Settings", "OPENAI_KEY", fallback=""))
            deepseek_key_entry.insert(0, config.get(
                "Settings", "DEEPSEEK_KEY", fallback=""))
            cozy_voice_key_entry.insert(0, config.get(
                "Settings", "DASHSCOPE_KEY", fallback=""))
            asr_server_entry.insert(0, config.get(
                "Settings", "ASR_SERVER", fallback=""))
            proxy_port_entry.insert(0, config.get(
                "Settings", "PROXY_PORT", fallback=""))


def save_settings():
    config = configparser.ConfigParser()
    config["Settings"] = {
        "OPENAI_KEY": openai_key_entry.get(),
        "DEEPSEEK_KEY": deepseek_key_entry.get(),
        "DASHSCOPE_KEY": cozy_voice_key_entry.get(),
        "ASR_SERVER": asr_server_entry.get(),
        "PROXY_PORT": proxy_port_entry.get()
    }
    with open(CONFIG_FILE, "w") as configfile:
        config.write(configfile)


def on_close():
    save_settings()
    root.destroy()


def check_python_installed():
    try:
        subprocess.check_output(["python", "--version"])
        return True
    except FileNotFoundError:
        return False


def setup_environment(alter=True):
    if not check_python_installed():
        install_python()
    elif alter:
        messagebox.showinfo("信息", "Python 环境已安装")


def install_python():
    python_installer = "static\\python-3.12.6-amd64.exe"
    if os.path.exists(python_installer):
        subprocess.run([python_installer], check=True)

        subprocess.run(
            ["python", "-m", "pip", "install", "-e", "."], check=True)
        messagebox.showinfo("信息", "Python 环境已安装并设置完成")
    else:
        messagebox.showerror("错误", "找不到Python安装包")


def can_access_google():
    try:
        response = requests.get("http://www.google.com", timeout=5)
        if response.status_code == 200:
            return True
    except requests.RequestException:
        return False
    return False


def set_proxy_env(port):
    if port == "-1":
        os.environ.pop('http_proxy', None)
        os.environ.pop('https_proxy', None)

    else:
        os.environ['http_proxy'] = f'http://127.0.0.1:{port}'
        os.environ['https_proxy'] = f'http://127.0.0.1:{port}'


def clear_proxy_env():
    os.environ.pop('http_proxy', None)
    os.environ.pop('https_proxy', None)


def auto_detect_proxy():
    clear_proxy_env()
    user_input_port = proxy_port_entry.get()
    if user_input_port:
        if check_proxy(user_input_port):
            set_proxy_env(user_input_port)
            return
        else:
            messagebox.showwarning(
                "警告", f"输入的代理端口 {user_input_port} 无效，正在尝试自动检测代理。")
    if can_access_google():
        set_proxy_env("-1")
        proxy_port_entry.delete(0, tk.END)
        proxy_port_entry.insert(0, "-1")
        messagebox.showinfo("信息", "已检测到本地可以直接访问Google，代理已禁用。")
        return

    default_ports = [7890, 1080, 8080]
    for port in default_ports:
        if check_proxy(port):
            set_proxy_env(port)
            proxy_port_entry.delete(0, tk.END)
            proxy_port_entry.insert(0, port)
            return

    messagebox.showwarning("警告", "未找到可用的代理端口")


def check_proxy(port):
    proxy_address = ("127.0.0.1", int(port))
    try:
        with socket.create_connection(proxy_address, timeout=5) as sock:
            connect_request = b"CONNECT www.google.com:443 HTTP/1.1\r\nHost: www.google.com:443\r\n\r\n"
            sock.sendall(connect_request)
            response = sock.recv(4096)
            if b"200 Connection established" in response:
                return True
    except socket.error:
        return False
    return False


def start_python_script(Alert=True):
    if check_singleton_title("Copilot_Whisper_Mic"):
        if Alert:
            messagebox.showinfo("信息", "Copilot_Whisper_Mic 已经在运行")
        return
    openai_key = openai_key_entry.get()
    deepseek_key = deepseek_key_entry.get()
    cozyvoice_key = cozy_voice_key_entry.get()

    proxy_port = proxy_port_entry.get()

    if not openai_key:
        messagebox.showerror("错误", "请设置OPENAI_API_KEY")
        return False

    if not proxy_port:
        messagebox.showerror("错误", "请设置代理端口或禁用代理")
        return False

    os.environ['OPENAI_API_KEY'] = openai_key
    os.environ['DEEPSEEK_API_KEY'] = deepseek_key
    if cozyvoice_key:
        os.environ['DASHSCOPE_API_KEY'] = cozyvoice_key
    set_proxy_env(proxy_port)
    mic_mode = selected_mic_version.get()
    if package_mode.get():
        command = ["cmd", "/k",
                   os.path.join("openra_ai", "OpenRA_Copilot.exe")]
    else:
        command = [os.path.join("openra_ai", "start.bat")]

    command.append("--remote-asr")

    if mic_mode == "whisper":
        command.append("--remote-type")
        command.append("whisper")

    if prompt_with_sample.get():
        command.append("--use-simplest-prompt")

    # if single_sample.get():
    # 默认开启这个吧，全Sample太难顶了
    command.append("--single-sample")


    if srtest_only.get():
        command.append("--openai-realtime-mode")
    else:
        command.append("--openai-response-mode")

    command.append("--gptmodel")
    command.append(selected_version.get())

    subprocess.Popen(command, env=os.environ,
                     creationflags=subprocess.CREATE_NEW_CONSOLE)

    return True


def start_openra(Alert=True):
    if not check_singleton("RedAlert.exe"):
        openra_path = "build/RedAlert.exe"
        subprocess.Popen([openra_path])
    elif Alert:
        messagebox.showinfo("信息", "OpenRA 已经在运行")


def one_click_start():
    setup_environment(False)
    if not proxy_port_entry.get():
        auto_detect_proxy()
    if start_python_script(False):
        start_openra(False)


def check_singleton(process_name):
    for proc in psutil.process_iter():
        try:
            if process_name.lower() in proc.name().lower():
                return True
        except psutil.NoSuchProcess:
            pass
    return False


def check_singleton_title(title):
    windows = gw.getWindowsWithTitle(title)
    if not windows:
        return False
    return True


def terminate_process_by_name(process_name):
    for proc in psutil.process_iter(['pid', 'name']):
        if proc.info['name'] == process_name:
            try:
                proc.terminate()
                proc.wait()
                return True
            except psutil.NoSuchProcess:
                return False
    return False


def terminate_process_by_window_title(title):
    windows = gw.getWindowsWithTitle(title)

    if not windows:
        return False

    for window in windows:
        hwnd = window._hWnd
        pid = ctypes.c_ulong()
        ctypes.windll.user32.GetWindowThreadProcessId(
            hwnd, ctypes.byref(pid))
        pid_value = pid.value

        try:

            for proc in psutil.process_iter(['pid', 'name']):
                if proc.pid == pid_value:
                    print(f"正在终止进程: {proc.info['name']} (PID: {proc.pid})")
                    proc.terminate()
                    proc.wait()
                    return True
        except psutil.NoSuchProcess:
            print(f"进程已不存在")
            return False

    return False


def get_latest_release(startup=False):
    try:
        url = "https://api.github.com/repos/OpenRA-CopilotTestGroup/OpenRA/releases"
        response = requests.get(url)
        releases = response.json()

        for release in releases:
            return release['tag_name'].lstrip('v'), release['assets'][0]['browser_download_url']
    except Exception as e:
        if not startup:
            messagebox.showerror("错误", f"检查更新时出现错误：{e}")


def check_for_updates(startup=False):
    global update_available, latest_version, download_url
    try:
        latest_version, download_url = get_latest_release()

        if latest_version > VERSION:
            update_available = True
            if startup:

                auto_update_button.config(text="有新版本！", bg="red", fg="white")
            else:
                prompt_update(latest_version, download_url)
        else:
            update_available = False
            if not startup:
                messagebox.showinfo("提示", "当前已是最新版本")

    except Exception as e:
        if not startup:
            messagebox.showerror("错误", f"检查更新时出现错误：{e}")


def prompt_update(latest_version, download_url):
    result = messagebox.askyesno("更新可用", f"检测到新版本 {latest_version}，是否下载并安装？")
    if result:
        download_and_replace(download_url)


def download_and_replace(download_url):
    def download_task(progress, root, label, speed_label):
        try:
            proxy_port = proxy_port_entry.get()
            proxies = {"http": f'http://127.0.0.1:{proxy_port}',
                       "https": f'http://127.0.0.1:{proxy_port}'} if proxy_port else None

            response = requests.get(download_url, stream=True, proxies=proxies)
            total_size = int(response.headers.get('content-length', 0))
            block_size = 65536
            zip_file = "launcher_update.zip"

            downloaded_size = 0
            start_time = time.time()
            if response.status_code == 200:
                with open(zip_file, "wb") as f:
                    for chunk in response.iter_content(block_size):
                        if chunk:
                            f.write(chunk)
                            downloaded_size += len(chunk)
                            root.update_idletasks()

                            percent = (downloaded_size / total_size) * 100
                            progress["value"] = percent
                            label.config(text=f"正在下载更新... {percent:.2f}%")

                            elapsed_time = time.time() - start_time
                            speed = downloaded_size / elapsed_time

                            if speed > 1024 * 1024:
                                speed_label.config(
                                    text=f"下载速度：{speed / (1024 * 1024):.2f} MB/s")
                            else:
                                speed_label.config(
                                    text=f"下载速度：{speed / 1024:.2f} KB/s")

                root.destroy()
                root.after(0, replace_and_restart_thread, zip_file, root)
            else:
                root.destroy()
                messagebox.showerror("错误", "下载更新失败")
        except Exception as e:
            root.after(0, root.destroy)
            messagebox.showerror("错误", f"下载更新时出现错误：{e}")

    def replace_and_restart_thread(zip_file, root):
        if not replace_and_restart(zip_file):
            messagebox.showerror("错误", "更新失败，请联系开发人员")

    def start_download():

        download_window = Tk()
        download_window.title("下载进度")
        download_window.geometry("300x150")

        progress = ttk.Progressbar(
            download_window, orient="horizontal", length=200, mode="determinate")
        progress.pack(pady=10)
        progress["maximum"] = 100

        label = ttk.Label(download_window, text="正在下载更新... 0%")
        label.pack()

        speed_label = ttk.Label(download_window, text="下载速度：0 KB/s")
        speed_label.pack()

        download_window.grab_set()

        download_thread = threading.Thread(target=download_task, args=(
            progress, download_window, label, speed_label))
        download_thread.start()

        download_window.mainloop()

    start_download()


def replace_and_restart(zip_file):
    result = messagebox.askokcancel("升级确认", "下载完成，确认升级将关闭当前进程")

    if result:
        print("继续执行后续操作")

    else:
        return
    terminate_process_by_name("RedAlert.exe")
    terminate_process_by_window_title("Copilot_Whisper_Mic")

    current_process_name = psutil.Process(os.getpid()).name()

    temp_dir = "temp_update"
    if not os.path.exists(temp_dir):
        os.makedirs(temp_dir)

    with zipfile.ZipFile(zip_file, 'r') as zip_ref:
        zip_ref.extractall(temp_dir)

    os.remove(zip_file)

    new_exe_file = os.path.join(temp_dir, "Starter.exe")

    if os.path.exists(new_exe_file):
        print(f"找到新的启动器: {new_exe_file}")
        shutil.move(new_exe_file, "Starter_new.exe")
        run_update_bat(current_process_name, temp_dir)
        return True
    else:
        print("未找到 Starter.exe 文件")
        return False


def run_update_bat(current_process_name, temp_dir):
    bat_content = f"""
        @echo off
        echo 升级中...请勿关闭此窗口
        timeout /t 5 /nobreak > NUL
        mkdir backup
        rmdir /s /q "backup\\build" >nul 2>nul
        rmdir /s /q "backup\\openra_ai" >nul 2>nul
        move build backup
        move openra_ai backup
        move /y "{current_process_name}" "backup\\Starter_old.exe"
        move /y "Starter_new.exe" "{current_process_name}"
        rmdir /s /q "build" >nul 2>nul
        rmdir /s /q "openra_ai" >nul 2>nul
        move {temp_dir}\\build build
        move {temp_dir}\\openra_ai openra_ai
        rmdir /s /q "{temp_dir}"
        start "" cmd /c del "%~f0"
        cls
        echo 升级完成！
        pause
        """
    with open("update.bat", "w") as f:
        f.write(bat_content)

    subprocess.Popen("update.bat", creationflags=subprocess.CREATE_NEW_CONSOLE)

    root.destroy()


def update_button():
    if not update_available:
        check_for_updates()
    if update_available:
        prompt_update(latest_version, download_url)


if not hasattr(sys, '_MEIPASS'):

    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    print(f"工作目录已更改为: {os.getcwd()}")

current_exe = sys.argv[0]
print(current_exe)
if "_new" in current_exe:
    new_exe_name = current_exe.replace("_new", "")
    time.sleep(1)
    shutil.copy(current_exe, new_exe_name)
    subprocess.Popen(new_exe_name)
    sys.exit()
update_available = False
check_for_updates(startup=True)

terminate_process_by_window_title("OpenRA - Red Alert")
root = tk.Tk()
root.title("Copilot-OpenRA启动器" + " v" + VERSION)

root.geometry("350x400")
root.configure(bg="#f0f0f0")


class GridConfig:
    def __init__(self, index, weight):
        self.index = index
        self.weight = weight


counter = 0


def ni():
    global counter
    idx = counter
    counter += 1
    return idx


class GridRows:
    global counter
    counter = 0

    HEADER = GridConfig(ni(), 1)
    OPENAI_KEY = GridConfig(ni(), 2)
    DEEPSEEK_KEY = GridConfig(ni(), 2)
    DASHSCOPE_KEY = GridConfig(ni(), 2)
    ASR_SERVER = GridConfig(ni(), 2)
    EXTRA_STARTPARAM = GridConfig(ni(), 2)
    PROXY_PORT = GridConfig(ni(), 2)
    CHECKS = GridConfig(ni(), 2)
    CHECKS2 = GridConfig(ni(), 2)
    DROPDOWNS = GridConfig(ni(), 3)
    BUTTONS = GridConfig(ni(), 3)
    ONE_CLICK = GridConfig(ni(), 6)
    FOOTER = GridConfig(ni(), 1)


class GridColumns:
    global counter
    counter = 0

    LEFT_PADDING = GridConfig(ni(), 1)
    CONTENT_LEFT = GridConfig(ni(), 8)
    CONTENT_RIGHT = GridConfig(ni(), 8)
    RIGHT_PADDING = GridConfig(ni(), 1)


# Configuring rows and columns dynamically using enumerations
for row_key, row_value in vars(GridRows).items():
    if isinstance(row_value, GridConfig):
        root.grid_rowconfigure(
            row_value.index, weight=row_value.weight, uniform="row")

for col_key, col_value in vars(GridColumns).items():
    if isinstance(col_value, GridConfig):
        root.grid_columnconfigure(
            col_value.index, weight=col_value.weight, uniform="col")


def create_labeled_entry(label_text, grid_row, label_columnspan, entry_columnspan, entry_width=None):
    tk.Label(root, text=label_text, bg="#f0f0f0").grid(
        row=grid_row.index, column=GridColumns.LEFT_PADDING.index,
        columnspan=label_columnspan, padx=(20, 0), pady=5, sticky="w"
    )
    entry = tk.Entry(
        root, width=entry_width) if entry_width else tk.Entry(root)
    entry.grid(
        row=grid_row.index, column=GridColumns.LEFT_PADDING.index,
        columnspan=entry_columnspan, padx=(120, 10), pady=5, sticky="we"
    )
    return entry


def create_checkbox(text, variable, grid_row, grid_column, entry_columnspan = 1):
    checkbox = tk.Checkbutton(
        root, text=text, variable=variable, compound="right")
    checkbox.grid(row=grid_row.index, column=grid_column.index,columnspan=entry_columnspan,
                  sticky="w", padx=(15, 15), pady=5)
    return checkbox


def create_button(text, command, grid_row, grid_column, font=None, **grid_options):
    button = tk.Button(root, text=text, command=command, font=font)
    button.grid(row=grid_row.index, column=grid_column.index, **grid_options)
    return button


# ---------------------参数框 start---------------------
openai_key_entry = create_labeled_entry(
    "OPENAI-KEY:", GridRows.OPENAI_KEY, 2, 3)

deepseek_key_entry = create_labeled_entry(
    "DEEPSEEK-KEY:", GridRows.DEEPSEEK_KEY, 2, 3)

cozy_voice_key_entry = create_labeled_entry(
    "CozyVoice-KEY:", GridRows.DASHSCOPE_KEY, 2, 3)

asr_server_entry = create_labeled_entry(
    "ASR SERVER:", GridRows.ASR_SERVER, 2, 3)


extra_param_entry = create_labeled_entry(
    "额外启动参数:", GridRows.EXTRA_STARTPARAM, 2, 3)

proxy_port_entry = create_labeled_entry(
    "设置代理端口:", GridRows.PROXY_PORT, 2, 2, entry_width=6)

# ---------------------参数框 end---------------------


# ---------------------CheckBox start---------------------

prompt_with_sample = tk.BooleanVar()
create_checkbox("简单Sample（效果可能会下降）", prompt_with_sample,
                GridRows.CHECKS, GridColumns.CONTENT_LEFT, 2)

srtest_only = tk.BooleanVar()
create_checkbox("过滤模式(嘈杂环境)", srtest_only, GridRows.CHECKS2,
                GridColumns.CONTENT_LEFT)

package_mode = tk.BooleanVar(value=True)
create_checkbox("打包模式", package_mode, GridRows.CHECKS2,
                GridColumns.CONTENT_RIGHT)

# single_sample = tk.BooleanVar(value=True)
# create_checkbox("仅单一Sample", single_sample,
#                 GridRows.CHECKS2, GridColumns.CONTENT_LEFT)

# ---------------------CheckBox end---------------------


# ---------------------下拉框 start---------------------

gpt_versions = ["gpt-4o",
                "gpt-4o mini", "gpt-o1", "ft:gpt-4o-2024-08-06:edaijia:openra-v0124:At9K9XP3", "deepseek-chat", "deepseek-reasoner"]

mic_versions = ["whisper", "手动输入", "fun_asr"]


def create_dropdown(root, variable, options, grid_row, grid_column):
    dropdown = tk.OptionMenu(root, variable, *options)
    dropdown.grid(row=grid_row.index, column=grid_column.index,
                  padx=(15, 15), pady=5, sticky="we")
    return dropdown


selected_version = StringVar(root)
selected_version.set("gpt-4o")
create_dropdown(
    root, selected_version, gpt_versions, GridRows.DROPDOWNS, GridColumns.CONTENT_LEFT)

selected_mic_version = StringVar(root)
selected_mic_version.set("whisper")
create_dropdown(
    root, selected_mic_version, mic_versions, GridRows.DROPDOWNS, GridColumns.CONTENT_RIGHT)

# ---------------------下拉框 end---------------------


# ---------------------按钮 start---------------------

button_font = ("Microsoft YaHei", 10)


start_python_button = create_button("启动AI助手", start_python_script, GridRows.BUTTONS,
                                    GridColumns.CONTENT_RIGHT, font=button_font, padx=(15, 15), pady=5, sticky="we")

start_openra_button = create_button("启动OpenRA", start_openra, GridRows.BUTTONS,
                                    GridColumns.CONTENT_LEFT, font=button_font, padx=(15, 15), pady=5, sticky="we")

auto_proxy_button = create_button("自动设置代理端口", auto_detect_proxy, GridRows.PROXY_PORT,
                                  GridColumns.CONTENT_RIGHT, font=button_font, padx=(15, 15), pady=2, sticky="we")

one_click_button = create_button("一键启动！", one_click_start, GridRows.ONE_CLICK, GridColumns.CONTENT_LEFT, font=(
    "Microsoft YaHei", 18), columnspan=2, pady=10, ipadx=50, ipady=10)

if update_available:
    auto_update_button = tk.Button(
        root, text="U！", command=update_button, font=button_font, width=3, bg="red", fg="white")
else:
    auto_update_button = tk.Button(
        root, text="U", command=update_button, font=button_font, width=3)

auto_update_button.grid(row=GridRows.ONE_CLICK.index, rowspan=2,
                        column=GridColumns.CONTENT_RIGHT.index, columnspan=2, padx=(10, 10), pady=(10, 10), sticky="es")

# ---------------------按钮 end---------------------

load_settings()

root.protocol("WM_DELETE_WINDOW", on_close)

root.mainloop()
