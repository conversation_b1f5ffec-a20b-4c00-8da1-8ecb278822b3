@echo off
chcp 65001 >nul

echo 🎮 Code Alert AI Agent 启动脚本
echo ==================================================
echo 🏆 Code Alert 黑客松大赛
echo 🚀 红警AI智能体控制界面
echo ==================================================

REM 检查conda是否可用
where conda >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到conda命令
    echo 请确保已安装Miniconda或Anaconda并添加到PATH
    pause
    exit /b 1
)

REM 激活conda环境
echo 🔧 激活conda环境: code-alert
call conda activate code-alert
if %errorlevel% neq 0 (
    echo ❌ 错误: 无法激活code-alert环境
    echo 请运行以下命令创建环境:
    echo conda create -n code-alert python=3.11 -y
    pause
    exit /b 1
)

REM 检查Python环境
echo 🐍 检查Python环境...
python --version

REM 启动GUI
echo 🚀 启动GUI界面...
python start_gui.py

echo 👋 GUI已关闭
pause
