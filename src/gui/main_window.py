"""
Code Alert AI Agent 主GUI界面
基于tkinter的图形用户界面，用于控制和监控AI Agent
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import asyncio
import json
import time
from datetime import datetime
import logging
from typing import Optional, Dict, Any

# 导入项目模块
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.agent import CodeAlertAgent
from config import get_settings
from utils.logger import setup_logging

logger = logging.getLogger(__name__)


class CodeAlertGUI:
    """Code Alert AI Agent 主GUI界面"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.agent: Optional[CodeAlertAgent] = None
        self.agent_thread: Optional[threading.Thread] = None
        self.is_running = False
        
        # 获取配置
        self.settings = get_settings()
        
        # 设置日志
        setup_logging(self.settings.log_level)
        
        # 初始化界面
        self.setup_ui()
        
        # 状态变量
        self.command_history = []
        self.game_status = "未连接"
        
    def setup_ui(self):
        """设置用户界面"""
        self.root.title("Code Alert AI Agent - 红警AI智能体")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f0f0f0')
        
        # 创建主框架
        self.create_main_frame()
        
        # 创建菜单栏
        self.create_menu()
        
        # 创建状态栏
        self.create_status_bar()
        
        # 绑定关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
    def create_main_frame(self):
        """创建主框架"""
        # 主容器
        main_container = ttk.Frame(self.root)
        main_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧面板 - 控制区域
        left_frame = ttk.LabelFrame(main_container, text="🎮 AI Agent 控制", padding=10)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=False, padx=(0, 5))
        left_frame.configure(width=400)
        
        # 右侧面板 - 监控区域
        right_frame = ttk.LabelFrame(main_container, text="📊 状态监控", padding=10)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))
        
        # 创建左侧控制面板
        self.create_control_panel(left_frame)
        
        # 创建右侧监控面板
        self.create_monitor_panel(right_frame)
        
    def create_control_panel(self, parent):
        """创建控制面板"""
        # Agent 状态
        status_frame = ttk.LabelFrame(parent, text="Agent 状态", padding=5)
        status_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.status_label = ttk.Label(status_frame, text="状态: 未启动", font=('Arial', 10, 'bold'))
        self.status_label.pack(anchor=tk.W)
        
        self.game_status_label = ttk.Label(status_frame, text="游戏: 未连接")
        self.game_status_label.pack(anchor=tk.W)
        
        # 控制按钮
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.start_button = ttk.Button(button_frame, text="🚀 启动 Agent", command=self.start_agent)
        self.start_button.pack(side=tk.LEFT, padx=(0, 5))
        
        self.stop_button = ttk.Button(button_frame, text="⏹️ 停止 Agent", command=self.stop_agent, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=(0, 5))
        
        self.test_button = ttk.Button(button_frame, text="🧪 测试连接", command=self.test_connection)
        self.test_button.pack(side=tk.LEFT)
        
        # 指令输入区域
        command_frame = ttk.LabelFrame(parent, text="🗣️ 指令输入", padding=5)
        command_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 指令输入框
        self.command_entry = ttk.Entry(command_frame, font=('Arial', 10))
        self.command_entry.pack(fill=tk.X, pady=(0, 5))
        self.command_entry.bind('<Return>', self.send_command)
        
        # 发送按钮
        send_button = ttk.Button(command_frame, text="📤 发送指令", command=self.send_command)
        send_button.pack()
        
        # 快捷指令
        shortcuts_frame = ttk.LabelFrame(parent, text="⚡ 快捷指令", padding=5)
        shortcuts_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建快捷指令按钮
        shortcuts = [
            ("🏗️ 建造电厂", "建造一个电厂"),
            ("🏭 建造兵营", "建造一个兵营"),
            ("👥 训练步兵", "训练步兵单位"),
            ("🔍 探索地图", "探索周围区域"),
            ("🛡️ 防守基地", "防守我们的基地"),
            ("⚔️ 攻击敌人", "攻击敌方单位"),
            ("💰 收集资源", "收集更多资源"),
            ("🔧 修复建筑", "修复受损的建筑")
        ]
        
        for i, (text, command) in enumerate(shortcuts):
            btn = ttk.Button(shortcuts_frame, text=text, 
                           command=lambda cmd=command: self.send_quick_command(cmd))
            btn.pack(fill=tk.X, pady=2)
            
    def create_monitor_panel(self, parent):
        """创建监控面板"""
        # 创建标签页
        notebook = ttk.Notebook(parent)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # 日志标签页
        log_frame = ttk.Frame(notebook)
        notebook.add(log_frame, text="📝 系统日志")
        
        self.log_text = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, font=('Consolas', 9))
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
        # 指令历史标签页
        history_frame = ttk.Frame(notebook)
        notebook.add(history_frame, text="📋 指令历史")
        
        self.history_text = scrolledtext.ScrolledText(history_frame, wrap=tk.WORD, font=('Consolas', 9))
        self.history_text.pack(fill=tk.BOTH, expand=True)
        
        # 游戏状态标签页
        game_frame = ttk.Frame(notebook)
        notebook.add(game_frame, text="🎮 游戏状态")
        
        self.game_text = scrolledtext.ScrolledText(game_frame, wrap=tk.WORD, font=('Consolas', 9))
        self.game_text.pack(fill=tk.BOTH, expand=True)
        
    def create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="保存日志", command=self.save_logs)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.on_closing)
        
        # 工具菜单
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="工具", menu=tools_menu)
        tools_menu.add_command(label="清空日志", command=self.clear_logs)
        tools_menu.add_command(label="清空历史", command=self.clear_history)
        
        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="关于", command=self.show_about)
        
    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = ttk.Frame(self.root)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
        self.status_text = ttk.Label(self.status_bar, text="就绪", relief=tk.SUNKEN, anchor=tk.W)
        self.status_text.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        self.time_label = ttk.Label(self.status_bar, text="", relief=tk.SUNKEN)
        self.time_label.pack(side=tk.RIGHT)
        
        # 更新时间
        self.update_time()
        
    def update_time(self):
        """更新时间显示"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.config(text=current_time)
        self.root.after(1000, self.update_time)
        
    def log_message(self, message: str, level: str = "INFO"):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        
        # 同时输出到控制台
        if level == "ERROR":
            logger.error(message)
        elif level == "WARNING":
            logger.warning(message)
        else:
            logger.info(message)
            
    def add_command_history(self, command: str, result: str):
        """添加指令历史"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        history_entry = f"[{timestamp}] 指令: {command}\n结果: {result}\n{'='*50}\n"
        
        self.history_text.insert(tk.END, history_entry)
        self.history_text.see(tk.END)
        
        self.command_history.append({
            'timestamp': timestamp,
            'command': command,
            'result': result
        })
        
    def update_game_status(self, status_data: Dict[str, Any]):
        """更新游戏状态显示"""
        self.game_text.delete(1.0, tk.END)
        
        formatted_status = json.dumps(status_data, indent=2, ensure_ascii=False)
        self.game_text.insert(tk.END, formatted_status)
        
    def start_agent(self):
        """启动AI Agent"""
        if self.is_running:
            self.log_message("Agent已在运行中", "WARNING")
            return
            
        try:
            self.log_message("正在启动AI Agent...")
            
            # 创建Agent实例
            self.agent = CodeAlertAgent()
            
            # 在后台线程中运行Agent
            self.agent_thread = threading.Thread(target=self.run_agent, daemon=True)
            self.agent_thread.start()
            
            self.is_running = True
            self.start_button.config(state=tk.DISABLED)
            self.stop_button.config(state=tk.NORMAL)
            
            self.status_label.config(text="状态: 运行中")
            self.status_text.config(text="AI Agent已启动")
            
            self.log_message("AI Agent启动成功！")
            
        except Exception as e:
            self.log_message(f"启动Agent失败: {str(e)}", "ERROR")
            messagebox.showerror("错误", f"启动Agent失败:\n{str(e)}")
            
    def run_agent(self):
        """在后台运行Agent"""
        try:
            # 这里可以添加Agent的主循环逻辑
            while self.is_running:
                time.sleep(1)  # 简单的心跳
                
        except Exception as e:
            self.log_message(f"Agent运行错误: {str(e)}", "ERROR")
            
    def stop_agent(self):
        """停止AI Agent"""
        if not self.is_running:
            self.log_message("Agent未在运行", "WARNING")
            return
            
        try:
            self.log_message("正在停止AI Agent...")
            
            self.is_running = False
            
            if self.agent_thread and self.agent_thread.is_alive():
                self.agent_thread.join(timeout=5)
                
            self.agent = None
            self.agent_thread = None
            
            self.start_button.config(state=tk.NORMAL)
            self.stop_button.config(state=tk.DISABLED)
            
            self.status_label.config(text="状态: 已停止")
            self.status_text.config(text="AI Agent已停止")
            
            self.log_message("AI Agent已停止")
            
        except Exception as e:
            self.log_message(f"停止Agent失败: {str(e)}", "ERROR")
            
    def test_connection(self):
        """测试游戏连接"""
        self.log_message("正在测试游戏连接...")
        
        # 这里添加实际的连接测试逻辑
        try:
            # 模拟连接测试
            time.sleep(1)
            self.game_status = "已连接"
            self.game_status_label.config(text=f"游戏: {self.game_status}")
            self.log_message("游戏连接测试成功")
            
            # 更新游戏状态显示
            test_status = {
                "connection": "成功",
                "game_mode": "测试模式",
                "player_units": 0,
                "enemy_units": 0,
                "resources": {"credits": 1000, "power": 50}
            }
            self.update_game_status(test_status)
            
        except Exception as e:
            self.log_message(f"连接测试失败: {str(e)}", "ERROR")
            self.game_status = "连接失败"
            self.game_status_label.config(text=f"游戏: {self.game_status}")
            
    def send_command(self, event=None):
        """发送指令"""
        command = self.command_entry.get().strip()
        if not command:
            return
            
        self.send_quick_command(command)
        self.command_entry.delete(0, tk.END)
        
    def send_quick_command(self, command: str):
        """发送快捷指令"""
        if not self.is_running:
            self.log_message("请先启动AI Agent", "WARNING")
            return
            
        self.log_message(f"发送指令: {command}")
        
        try:
            # 这里添加实际的指令处理逻辑
            # 目前使用模拟响应
            result = f"指令 '{command}' 已接收并处理"
            
            self.add_command_history(command, result)
            self.log_message(f"指令执行结果: {result}")
            
        except Exception as e:
            error_msg = f"指令执行失败: {str(e)}"
            self.log_message(error_msg, "ERROR")
            self.add_command_history(command, error_msg)
            
    def save_logs(self):
        """保存日志"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"code_alert_logs_{timestamp}.txt"
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("=== Code Alert AI Agent 日志 ===\n")
                f.write(f"生成时间: {datetime.now()}\n\n")
                f.write("=== 系统日志 ===\n")
                f.write(self.log_text.get(1.0, tk.END))
                f.write("\n=== 指令历史 ===\n")
                f.write(self.history_text.get(1.0, tk.END))
                
            self.log_message(f"日志已保存到: {filename}")
            messagebox.showinfo("成功", f"日志已保存到:\n{filename}")
            
        except Exception as e:
            self.log_message(f"保存日志失败: {str(e)}", "ERROR")
            messagebox.showerror("错误", f"保存日志失败:\n{str(e)}")
            
    def clear_logs(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        self.log_message("日志已清空")
        
    def clear_history(self):
        """清空历史"""
        self.history_text.delete(1.0, tk.END)
        self.command_history.clear()
        self.log_message("指令历史已清空")
        
    def show_about(self):
        """显示关于对话框"""
        about_text = """Code Alert AI Agent v1.0

🎮 红警AI智能体控制界面

🏆 为Code Alert黑客松大赛开发
🚀 基于OpenRA游戏引擎
🧠 支持自然语言指令控制

开发者: AI Assistant
技术栈: Python + FastAPI + OpenRA + tkinter"""
        
        messagebox.showinfo("关于", about_text)
        
    def on_closing(self):
        """关闭程序时的处理"""
        if self.is_running:
            if messagebox.askokcancel("退出", "AI Agent正在运行，确定要退出吗？"):
                self.stop_agent()
                self.root.destroy()
        else:
            self.root.destroy()
            
    def run(self):
        """运行GUI"""
        self.log_message("Code Alert AI Agent GUI 启动")
        self.log_message("🎮 欢迎使用红警AI智能体控制界面！")
        self.log_message("💡 点击'启动 Agent'开始，然后可以发送指令控制游戏")
        
        self.root.mainloop()


def main():
    """主函数"""
    try:
        app = CodeAlertGUI()
        app.run()
    except Exception as e:
        print(f"GUI启动失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
