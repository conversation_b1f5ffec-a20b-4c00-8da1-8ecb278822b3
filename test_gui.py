#!/usr/bin/env python3
"""
GUI功能测试脚本
用于验证GUI组件是否正常工作
"""

import sys
import os
import time

def test_imports():
    """测试必要的模块导入"""
    print("🧪 测试模块导入...")
    
    try:
        import tkinter as tk
        print("✅ tkinter 导入成功")
    except ImportError as e:
        print(f"❌ tkinter 导入失败: {e}")
        return False
        
    try:
        from tkinter import ttk, scrolledtext, messagebox
        print("✅ tkinter 扩展模块导入成功")
    except ImportError as e:
        print(f"❌ tkinter 扩展模块导入失败: {e}")
        return False
        
    try:
        import threading
        import json
        import subprocess
        from datetime import datetime
        print("✅ 标准库模块导入成功")
    except ImportError as e:
        print(f"❌ 标准库模块导入失败: {e}")
        return False
        
    return True

def test_gui_creation():
    """测试GUI创建"""
    print("🖼️ 测试GUI创建...")
    
    try:
        import tkinter as tk
        
        # 创建测试窗口
        root = tk.Tk()
        root.title("GUI测试")
        root.geometry("300x200")
        
        # 添加测试组件
        label = tk.Label(root, text="GUI测试成功！")
        label.pack(pady=20)
        
        button = tk.Button(root, text="关闭", command=root.destroy)
        button.pack(pady=10)
        
        print("✅ GUI组件创建成功")
        
        # 显示窗口2秒后自动关闭
        root.after(2000, root.destroy)
        root.mainloop()
        
        print("✅ GUI显示和关闭成功")
        return True
        
    except Exception as e:
        print(f"❌ GUI创建失败: {e}")
        return False

def test_project_structure():
    """测试项目结构"""
    print("📁 测试项目结构...")
    
    required_files = [
        "start_gui.py",
        "src/main.py",
        "src/config.py",
        "src/core/agent.py",
        "GUI_README.md"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
            
    if missing_files:
        print(f"❌ 缺少文件: {missing_files}")
        return False
    else:
        print("✅ 项目结构完整")
        return True

def test_conda_environment():
    """测试conda环境"""
    print("🐍 测试conda环境...")
    
    try:
        import subprocess
        
        # 检查conda命令
        result = subprocess.run(['conda', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ conda版本: {result.stdout.strip()}")
        else:
            print("❌ conda命令不可用")
            return False
            
        # 检查Python版本
        result = subprocess.run([sys.executable, '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ Python版本: {result.stdout.strip()}")
        else:
            print("❌ Python版本检查失败")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ 环境检查失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🎮 Code Alert AI Agent GUI 测试")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_imports),
        ("项目结构", test_project_structure),
        ("conda环境", test_conda_environment),
        ("GUI创建", test_gui_creation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 运行测试: {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！GUI可以正常使用")
        print("\n🚀 启动GUI命令:")
        print("   python start_gui.py")
        print("   或者运行: ./start.sh (macOS/Linux)")
        print("   或者运行: start.bat (Windows)")
    else:
        print("⚠️ 部分测试失败，请检查环境配置")
        
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
