# Code Alert AI Agent GUI 使用指南

🎮 **红警AI智能体图形用户界面**

## 🚀 快速启动

### macOS/Linux
```bash
./start.sh
```

### Windows
```cmd
start.bat
```

### 手动启动
```bash
# 激活conda环境
conda activate code-alert

# 启动GUI
python start_gui.py
```

## 📋 功能特性

### 🎛️ 控制面板
- **启动/停止服务器**: 控制AI Agent后端服务
- **状态监控**: 实时显示服务器运行状态
- **快捷操作**: 一键打开Web界面、测试API连接

### 📝 日志系统
- **实时日志**: 显示服务器运行日志和系统消息
- **时间戳**: 每条日志都有精确的时间记录
- **日志级别**: 区分INFO、WARNING、ERROR等不同级别

### 🌐 Web界面集成
- **自动启动**: GUI可以自动启动FastAPI后端服务
- **浏览器集成**: 一键打开Web控制界面
- **API测试**: 内置API连接测试功能

## 🎯 使用流程

1. **启动GUI**: 运行启动脚本或手动启动
2. **启动服务器**: 点击"启动 AI Agent 服务器"按钮
3. **打开Web界面**: 点击"打开Web界面"按钮
4. **发送指令**: 在Web界面中输入自然语言指令
5. **观察结果**: 在GUI日志中查看执行结果

## 🎮 支持的指令类型

### 🏗️ 建造指令
- "建造一个电厂"
- "建造兵营"
- "建设防御塔"

### 👥 单位控制
- "训练步兵单位"
- "派遣单位到指定位置"
- "攻击敌方目标"

### 🔍 探索指令
- "探索周围区域"
- "侦察敌方基地"
- "揭开战争迷雾"

### 🛡️ 防守指令
- "防守我们的基地"
- "建立防御阵线"
- "修复受损建筑"

### ⚔️ 攻击指令
- "攻击敌方单位"
- "全面进攻"
- "摧毁敌方建筑"

### 💰 资源管理
- "收集更多资源"
- "增加电力生产"
- "优化资源分配"

## 🔧 技术架构

```
GUI界面 (tkinter)
    ↓
FastAPI服务器 (后端)
    ↓
AI Agent核心 (NLP + 决策)
    ↓
OpenRA游戏引擎 (Socket API)
```

## 📊 界面组件

### 主窗口布局
- **标题栏**: 项目名称和描述
- **控制面板**: 服务器控制和快捷操作
- **日志面板**: 实时系统日志显示
- **状态栏**: 当前状态和时间显示

### 控制按钮
- 🚀 **启动服务器**: 启动AI Agent后端服务
- ⏹️ **停止服务器**: 停止后端服务
- 🌐 **打开Web界面**: 在浏览器中打开控制界面
- 🧪 **测试API**: 测试后端API连接
- 🗑️ **清空日志**: 清除日志显示

## 🌐 Web界面访问

启动服务器后，可以通过以下地址访问：

- **主页**: http://localhost:8000
- **API文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/ping

## 🐛 故障排除

### 常见问题

1. **GUI无法启动**
   - 检查是否激活了正确的conda环境
   - 确保Python版本为3.11+
   - 检查tkinter是否可用: `python -c "import tkinter"`

2. **服务器启动失败**
   - 检查端口8000是否被占用
   - 确保所有依赖包已安装
   - 查看日志中的错误信息

3. **Web界面无法访问**
   - 确认服务器已成功启动
   - 检查防火墙设置
   - 尝试使用127.0.0.1:8000替代localhost:8000

### 日志文件位置
- GUI日志: 显示在界面中，可通过"清空日志"按钮清除
- 服务器日志: 在`data/logs/`目录下
- 错误日志: 同时显示在GUI和控制台中

## 🎯 开发信息

- **项目**: Code Alert AI Agent
- **比赛**: Code Alert 黑客松大赛
- **技术栈**: Python + FastAPI + OpenRA + tkinter
- **版本**: v1.0.0

## 📞 支持

如果遇到问题，请：
1. 查看GUI日志面板中的错误信息
2. 检查控制台输出
3. 确认环境配置是否正确
4. 参考项目文档和部署日志

---

🏆 **祝您在Code Alert黑客松大赛中取得好成绩！**
