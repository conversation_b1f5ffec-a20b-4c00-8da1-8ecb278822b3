#!/bin/bash

# Code Alert AI Agent 启动脚本
# 用于快速启动GUI界面

echo "🎮 Code Alert AI Agent 启动脚本"
echo "=" * 50
echo "🏆 Code Alert 黑客松大赛"
echo "🚀 红警AI智能体控制界面"
echo "=" * 50

# 检查conda是否可用
if ! command -v conda &> /dev/null; then
    echo "❌ 错误: 未找到conda命令"
    echo "请确保已安装Miniconda或Anaconda"
    exit 1
fi

# 激活conda环境
echo "🔧 激活conda环境: code-alert"
source $(conda info --base)/etc/profile.d/conda.sh
conda activate code-alert

if [ $? -ne 0 ]; then
    echo "❌ 错误: 无法激活code-alert环境"
    echo "请运行以下命令创建环境:"
    echo "conda create -n code-alert python=3.11 -y"
    exit 1
fi

# 检查Python环境
echo "🐍 检查Python环境..."
python --version

# 启动GUI
echo "🚀 启动GUI界面..."
python start_gui.py

echo "👋 GUI已关闭"
