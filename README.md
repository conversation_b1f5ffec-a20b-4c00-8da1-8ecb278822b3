# Code Alert AI Agent - 红警AI智能体

> 探索 AI智能体 与实时战略游戏的融合，打造 AI 战役指挥官

## 🎯 项目概述

本项目是为 **Code Alert AI 黑客松大赛** 开发的AI智能体，能够通过中文自然语言指令控制红色警戒游戏，完成各种战略任务。基于OpenRA引擎，使用Python + FastAPI构建，集成大语言模型进行指令理解和决策。

### 🏆 比赛信息
- **主办方**: GOSIM 大会
- **初赛**: 2025年8月4日 - 9月1日 (线上文本指令)
- **决赛**: 2025年9月13-14日 (杭州现场语音指令)
- **游戏引擎**: OpenRA (GitHub Stars: 15,700+)
- **挑战**: 20个关卡，涵盖6大战略场景类型

### 🎮 核心能力
- **自然语言理解**: 解析复杂的中文军事战术指令
- **实时决策**: 快速响应和执行游戏操作 (< 2秒)
- **战略规划**: 支持建设、探索、防守、进攻、撤退等多种战术
- **多单位协调**: 实现复杂的多单位协同作战
- **图形化界面**: 提供直观的GUI控制界面

## 🚀 快速启动

### GUI界面启动 (推荐)

**macOS/Linux:**
```bash
./start.sh
```

**Windows:**
```cmd
start.bat
```

**手动启动:**
```bash
# 激活conda环境
conda activate code-alert

# 启动GUI界面
python start_gui.py
```

### 命令行启动
```bash
# 激活环境
conda activate code-alert

# 启动Web服务器
python -m src.main

# 访问Web界面: http://localhost:8000
```

### 环境测试
```bash
# 运行环境和GUI测试
python test_gui.py
```

## 🏗️ 系统架构

### 整体架构
```
┌─────────────────────────────────────────────────────────────┐
│                    用户接口层                                │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │  Web API    │  │  CLI工具    │  │  语音接口   │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                   AI Agent 核心层                           │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              指令处理流水线                              │ │
│  │  NLP解析 → 状态分析 → 决策规划 → 动作执行 → 结果反馈    │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    游戏接口层                               │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │ OpenRA API  │  │  状态监控   │  │  日志收集   │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
```

### 核心组件
- **🧠 NLP处理模块**: 混合方法 (规则匹配 + LLM兜底 + 学习缓存)
- **🎮 游戏接口模块**: OpenRA RESTful API客户端，支持重试和错误恢复
- **⚡ 决策引擎**: 分层决策架构 (战略层/战术层/操作层)
- **📊 状态管理**: 实时游戏状态感知和多维度建模
- **🔧 工具模块**: 异步处理、智能缓存、性能监控

## 🚀 技术栈

### 核心技术选择
- **开发语言**: Python 3.11+ (AI生态丰富，开发效率高)
- **Web框架**: FastAPI + uvicorn (高性能异步框架)
- **AI/ML**: OpenAI GPT-4, spaCy, transformers (混合NLP方案)
- **游戏接口**: OpenRA Socket API (TCP 7445端口，JSON格式)
- **游戏引擎**: 官方定制OpenRA (OpenRA-CopilotTestGroup/OpenRA)
- **异步处理**: asyncio, socket (高并发Socket通信)
- **数据处理**: pydantic, numpy, pandas

### 关键依赖
```txt
# 核心框架
fastapi==0.104.1          # Web框架
uvicorn==0.24.0           # ASGI服务器
pydantic==2.5.0           # 数据验证

# AI/ML
openai==1.3.0             # OpenAI API
spacy==3.7.0              # NLP处理
transformers>=4.21.0      # 预训练模型
scikit-learn>=1.3.0       # 机器学习

# 网络通信
aiohttp==3.9.0            # 异步HTTP客户端
websockets>=11.0          # WebSocket支持

# 工具库
pytest==7.4.0            # 测试框架
cachetools>=5.3.0         # 缓存工具
```

## 📁 项目结构

```
code-alert-ai-agent/
├── src/                    # 源代码
│   ├── core/              # 核心模块 (Agent, 游戏状态, 指令执行)
│   ├── nlp/               # NLP处理 (解析器, 意图分类, 实体提取)
│   ├── game/              # 游戏接口 (OpenRA客户端, 状态监控)
│   ├── strategy/          # 策略决策 (决策引擎, 战术规划)
│   ├── utils/             # 工具模块 (日志, 缓存, 监控)
│   └── api/               # Web API接口
├── tests/                 # 测试代码
├── data/                  # 数据文件 (配置, 模型, 日志)
├── scripts/               # 脚本工具
└── docs/                  # 文档
```

## 🎮 官方关卡挑战 (已发布5个，预计20个)

### 🏗️ Mission-01 基础建造 (90秒)
- **目标**: 部署基地车，建造完整的基础设施
- **具体任务**:
  - 部署基地车 (MCV)
  - 建造1个电厂、1个矿场、1个战车工厂、1个雷达站、1个核电厂
  - 生产3个步兵、2个移动防空车
- **考验**: AI对建筑依赖关系的理解和执行效率

### 🔍 Mission-02 战争迷雾 (100秒)
- **目标**: 使用雅克战机消除≥80%的战争迷雾
- **具体任务**:
  - 控制雅克战机进行地图探索
  - 高效规划飞行路径
- **考验**: AI的信息处理、规划能力和决策速度

### 🏗️ Mission-03 高级建造 (120秒)
- **目标**: 大规模生产和建设
- **具体任务**:
  - 建造1个电厂、1个战车工厂
  - 生产10个步兵、10个炮兵、1个矿车、1个移动防空车
- **考验**: 建筑摆放位置优化和大规模生产管理

### ⚔️ Mission-04 空中打击
- **状态**: 待发布
- **预期**: 空战相关的战术指挥

### 🛡️ Mission-05 基础防御
- **状态**: 待发布
- **预期**: 防御体系构建和资源管理

### 🔮 未来关卡 (Mission-06 到 Mission-20)
- **战略撤退场景**: 劣势判断和战略性撤退
- **复杂进攻场景**: 双路夹击等高级战术
- **多单位协调**: 空地协同作战
- **资源管理**: 经济系统优化

## 💬 指令示例

### 🏗️ 建造指令
```
"建造一个电厂"                    → BuildStructure(PowerPlant)
"在基地附近造兵营"                → BuildStructure(Barracks, NearBase)
"建设防御塔保护基地"              → BuildStructure(DefenseTower, DefensePosition)
"增加电力生产"                    → IncreasePowerProduction()
```

### 🎯 单位控制指令
```
"派遣步兵到A点探索"               → MoveUnit(Infantry, PositionA, Explore)
"让坦克攻击敌方基地"              → AttackTarget(Tank, EnemyBase)
"所有单位撤退到安全区域"          → Retreat(AllUnits, SafeZone)
"修复受损的建筑"                  → RepairBuildings()
```

### ⚔️ 战略指令
```
"探索整个地图"                    → ExploreMap(FullMap)
"全面防守基地"                    → DefendBase(FullDefense)
"发动总攻"                        → FullAttack(AllUnits)
"收集更多资源"                    → CollectResources(Optimize)
```

### 🧠 复杂战术指令
```
"用步兵从北面佯攻，坦克从南面主攻"  → CoordinatedAttack(Infantry:North:Feint, Tank:South:Main)
"派遣侦察机探索敌方基地位置"        → ScoutMission(Aircraft, EnemyBaseLocation)
"建立防御阵地阻止敌人进攻"          → EstablishDefenseLine(BlockEnemyAdvance)
```

## � 硬件要求与开发环境

### 🖥️ 推荐硬件配置

#### 最低配置
- **CPU**: 4核心 2.5GHz+ (支持多线程)
- **内存**: 8GB RAM (推荐16GB+)
- **存储**: 20GB 可用空间 (SSD推荐)
- **网络**: 稳定的互联网连接 (用于API调用)

#### 推荐配置
- **CPU**: 8核心+ (Intel i7/AMD Ryzen 7 或 Apple M1/M2)
- **内存**: 32GB+ RAM (用于大模型和并发处理)
- **存储**: 100GB+ SSD (用于缓存和日志)
- **GPU**: 可选，用于本地模型推理加速

#### 开发环境配置 (当前)
- **设备**: Mac Studio (2023) - Mac14,14
- **芯片**: Apple M2 Ultra (24核心)
- **内存**: 192 GB 统一内存
- **存储**: 477 GB SSD + 多个外接硬盘
- **系统**: macOS 15.5 (Sequoia) ARM64

### 🛠️ 支持的操作系统
- **macOS**: 10.15+ (推荐 11.0+)
- **Windows**: Windows 10/11 (64位)
- **Linux**: Ubuntu 18.04+, CentOS 7+, Debian 10+

### 🐍 Python环境要求
- **Python版本**: 3.9+ (推荐 3.11+)
- **包管理器**: pip 21.0+ 或 conda
- **虚拟环境**: venv, conda, 或 poetry

## 🔧 快速开始

### 1. 环境准备
```bash
# 安装 .NET 6.0 x64 (必需)
# 下载地址: https://dotnet.microsoft.com/en-us/download/dotnet/6.0

# macOS 用户需要安装 Rosetta (M系列芯片)
/usr/sbin/softwareupdate --install-rosetta --agree-to-license

# 克隆我们的项目
git clone <repository-url>
cd code-alert-ai-agent

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt
```

### 2. 安装官方OpenRA引擎
```bash
# 克隆官方定制版OpenRA
git clone https://github.com/OpenRA-CopilotTestGroup/OpenRA.git
cd OpenRA

# 构建游戏引擎
dotnet restore --source https://nuget.cdn.azure.cn/v3/index.json  # 可选：国内CDN
dotnet build

# macOS/Linux 启动游戏
export PATH="/usr/local/share/dotnet/x64:$PATH"
./launch-game.sh Game.Mod=copilot

# Windows 启动游戏
.\launch-game.cmd Game.Mod=copilot
```

### 3. 启动AI Agent
```bash
# 返回项目目录
cd ../code-alert-ai-agent

# 启动主服务
python -m src.main

# 或直接运行特定关卡
python -m src.challenges.level_01_basic_building
```

### 4. 测试Socket连接
```python
# 测试官方Socket API (端口7445)
import socket
import json
import uuid

payload = {
    "apiVersion": "1.0",
    "requestId": str(uuid.uuid4()),
    "command": "deploy",
    "params": {
        "targets": {
            "type": ["基地车"],
            "faction": "己方"
        }
    },
    "language": "zh"
}

data = json.dumps(payload, ensure_ascii=False).encode("utf-8")

with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
    s.connect(("127.0.0.1", 7445))
    s.sendall(data)
```

## 🧪 测试

```bash
# 运行所有测试
pytest

# 运行特定测试
pytest tests/unit/test_nlp.py

# 测试覆盖率
pytest --cov=src tests/
```

## 📊 性能指标与优化

### 🎯 目标指标
- **响应时间**: < 2秒 (常用指令 < 10ms，复杂指令 < 1000ms)
- **指令准确率**: > 85% (规则匹配 > 95%，LLM解析 > 90%)
- **系统稳定性**: > 95% (异常恢复率 > 99%)
- **关卡完成率**: 100% (20/20关卡全部通过)

### ⚡ 性能优化策略

#### 混合NLP处理
```python
# 三层处理机制
1. 规则匹配 (< 10ms) - 处理常见指令
2. 学习缓存 (< 5ms) - 缓存历史解析结果
3. LLM解析 (200-1000ms) - 处理复杂指令
```

#### 异步架构优化
- **并发处理**: 同时执行NLP解析、状态获取、相似指令查找
- **连接池**: 复用HTTP连接减少开销
- **重试机制**: 指数退避重试策略
- **降级策略**: API异常时使用备用方案

#### 缓存策略
- **指令缓存**: TTL缓存 (5分钟) 存储解析结果
- **状态缓存**: LRU缓存 (100条) 存储游戏状态
- **策略缓存**: 预计算常见战术和路径

## 📈 开发计划

### 🚀 Week 1: 基础框架 (8/4-8/10)
**目标**: 搭建MVP，完成第一个关卡

- [x] 环境搭建和OpenRA API调研
- [x] 项目基础结构搭建
- [ ] 基础游戏客户端实现
- [ ] 简单NLP解析器开发
- [ ] 第一个关卡完成测试

### ⚡ Week 2: 核心功能 (8/11-8/17)
**目标**: 完成核心功能，支持前10个关卡

- [ ] 混合NLP处理模块完善
- [ ] 分层决策引擎实现
- [ ] 游戏状态感知系统
- [ ] 前10个关卡支持
- [ ] 基础性能优化

### 🎯 Week 3: 高级功能 (8/18-8/24)
**目标**: 完成所有关卡，实现复杂战术

- [ ] 复杂战术指令支持
- [ ] 多单位协调作战
- [ ] 高级决策算法
- [ ] 全部20个关卡完成
- [ ] 系统稳定性提升

### 🏆 Week 4: 优化准备 (8/25-9/1)
**目标**: 最终优化，决赛准备

- [ ] 语音接口开发 (Moxin-7B集成)
- [ ] 大量测试和性能调优
- [ ] 错误处理和恢复机制
- [ ] 决赛环境适配
- [ ] 文档完善和部署准备

## 🏆 比赛目标

### 🎯 初赛目标 (线上文本指令)
- ✅ **关卡完成**: 完成所有20个关卡挑战
- 🎯 **排名目标**: 排行榜前6名 (获得决赛资格)
- ⚡ **性能指标**: 指令响应时间 < 2秒
- 📊 **准确率**: 指令理解准确率 > 85%

### 🏅 决赛目标 (现场语音指令)
- 🎤 **语音识别**: 语音指令准确识别率 > 90%
- ⚔️ **对战能力**: 1v1对战胜率 > 60%
- 🚀 **实时响应**: 语音到执行延迟 < 3秒
- 🥇 **最终目标**: 争取前三名

## � 关键技术挑战与解决方案

### 挑战1: 自然语言理解
- **问题**: 理解复杂的军事战术指令
- **解决方案**:
  - 构建领域特定词汇表和规则库
  - 使用GPT-4进行复杂指令解析
  - 实现上下文记忆和学习机制

### 挑战2: 实时决策
- **问题**: 快速响应和执行指令
- **解决方案**:
  - 异步处理架构，并发执行多任务
  - 预计算常见策略和路径
  - 优先级队列管理紧急指令

### 挑战3: 游戏状态理解
- **问题**: 准确感知复杂游戏状态
- **解决方案**:
  - 多维度状态建模 (地图、单位、资源、目标)
  - 实时数据同步和状态变化检测
  - 智能缓存减少API调用开销

### 挑战4: 战略规划
- **问题**: 制定有效的长期和短期策略
- **解决方案**:
  - 分层决策架构 (战略层/战术层/操作层)
  - 基于规则的专家系统
  - 强化学习优化决策质量

## 🚨 风险评估与应对

### 🔴 高风险项
1. **OpenRA API学习曲线**: 可能比预期复杂
   - **应对**: 提前深入研究文档，准备Socket接口备选方案
2. **NLP准确性**: 中文指令理解可能不够准确
   - **应对**: 建立测试数据集，多种方法结合验证
3. **时间不足**: 20个关卡开发时间紧张
   - **应对**: 优先级管理，核心功能优先，功能裁剪预案

### � 中风险项
1. **性能问题**: Python性能可能不够
   - **应对**: 异步优化，关键部分用Cython加速
2. **API稳定性**: 游戏API可能不稳定
   - **应对**: 重试机制，错误处理，多接口备选

## 🧪 测试策略

### 单元测试
- NLP模块指令解析准确性测试
- 游戏状态更新正确性测试
- 决策引擎逻辑测试
- API接口调用测试

### 集成测试
- 端到端指令执行测试
- 多关卡场景测试
- 性能压力测试
- 异常情况处理测试

### 用户测试
- 自然语言指令理解测试
- 游戏体验流畅度测试
- 复杂场景应对能力测试

## 🚨 重要发现与行动计划

### 📋 官方代码分析结果

通过分析官方代码仓库 `https://github.com/OpenCodeAlert/Hackathon2025.git`，我们发现了关键信息：

#### ✅ 已确认的官方信息
- **官方关卡**: 已发布5个关卡，预计总共20个
- **游戏引擎**: 官方定制版 `OpenRA-CopilotTestGroup/OpenRA`
- **API协议**: Socket TCP 7445端口，JSON格式
- **评估标准**: 关卡完成时间、API调用次数、人工指令数量

#### 🔄 需要调整的部分
1. **游戏引擎**: 从标准OpenRA切换到官方定制版
2. **API接口**: 从HTTP RESTful切换到Socket JSON
3. **关卡系统**: 适配官方的5个具体关卡
4. **评估指标**: 对齐官方的评分标准

### 🎯 下一步行动计划

#### 立即行动 (本周)
1. **安装官方OpenRA**: 下载并配置官方定制版游戏引擎
2. **Socket API集成**: 重构游戏接口，使用Socket连接
3. **关卡适配**: 针对官方Mission-01到Mission-05进行适配

#### 短期目标 (2周内)
1. **完成前5个关卡**: 确保能通过所有已发布的官方关卡
2. **性能优化**: 优化API调用次数和响应时间
3. **测试验证**: 大量测试确保稳定性

## 🔗 相关链接

- [比赛官方代码](https://github.com/OpenCodeAlert/Hackathon2025.git) - 官方关卡和API
- [官方OpenRA引擎](https://github.com/OpenRA-CopilotTestGroup/OpenRA) - 定制版游戏引擎
- [比赛官网](https://hackathon.scimigo.com/) - Code Alert AI 黑客松
- [GOSIM 大会](https://hangzhou2025.gosim.org/zh/) - 杭州GOSIM大会官网
- [OpenAI API](https://platform.openai.com/docs) - GPT-4 API文档

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

---

**🚀 让AI指挥官带领我们在Code Alert AI黑客松中走向胜利！**

*项目创建时间: 2025-08-04 | 最后更新: 2025-08-04*
