2025-08-04 02:15:52 | INFO     | root | 日志系统初始化完成，级别: INFO
2025-08-04 02:15:53 | INFO     | src.nlp.parser | spaCy中文模型加载成功
2025-08-04 02:15:53 | INFO     | src.nlp.parser | NLP解析器初始化完成
2025-08-04 02:15:53 | INFO     | src.nlp.parser | 解析指令: 建造一个电厂
2025-08-04 02:15:53 | INFO     | src.nlp.parser | 规则匹配成功: build_structure
2025-08-04 02:15:53 | INFO     | src.nlp.parser | 解析指令: 派遣步兵到A点
2025-08-04 02:15:53 | INFO     | src.nlp.parser | 规则匹配成功: move_unit
2025-08-04 02:15:53 | INFO     | src.nlp.parser | 解析指令: 攻击敌方基地
2025-08-04 02:15:53 | INFO     | src.nlp.parser | 规则匹配成功: attack_target
2025-08-04 02:15:53 | INFO     | src.nlp.parser | 解析指令: 探索地图
2025-08-04 02:15:53 | INFO     | src.nlp.parser | 规则匹配成功: explore_map
2025-08-04 02:15:53 | INFO     | src.nlp.parser | 解析指令: 防守基地
2025-08-04 02:15:53 | INFO     | src.nlp.parser | 规则匹配成功: defend_base
2025-08-04 02:15:53 | INFO     | src.nlp.parser | 解析指令: 撤退到安全区域
2025-08-04 02:15:53 | INFO     | src.nlp.parser | 规则匹配成功: retreat
2025-08-04 02:15:53 | INFO     | src.nlp.parser | 解析指令: 收集资源
2025-08-04 02:15:53 | INFO     | src.nlp.parser | 规则匹配成功: collect_resources
2025-08-04 02:15:53 | INFO     | src.nlp.parser | 解析指令: 修复建筑
2025-08-04 02:15:53 | INFO     | src.nlp.parser | 规则匹配成功: repair
2025-08-04 02:15:53 | INFO     | src.nlp.parser | 解析指令: 这是一个无法识别的指令
2025-08-04 02:15:53 | WARNING  | src.nlp.parser | 无法识别指令: 这是一个无法识别的指令
2025-08-04 02:15:53 | INFO     | src.game.openra_client | OpenRA客户端初始化，服务器: http://localhost:8080
2025-08-04 02:15:53 | WARNING  | src.game.openra_client | 使用模拟连接模式
2025-08-04 02:15:53 | INFO     | src.game.openra_client | OpenRA服务器连接成功
2025-08-04 02:15:53 | INFO     | src.game.openra_client | 获取游戏状态成功
2025-08-04 02:15:53 | INFO     | src.game.openra_client | 执行游戏指令: {'action': 'build_structure', 'structure_type': 'power_plant', 'position': {'x': 100, 'y': 100}}
2025-08-04 02:15:53 | INFO     | src.game.openra_client | 游戏指令执行成功
2025-08-04 02:15:53 | INFO     | src.game.openra_client | OpenRA服务器连接已断开
2025-08-04 02:15:53 | INFO     | src.nlp.parser | spaCy中文模型加载成功
2025-08-04 02:15:53 | INFO     | src.nlp.parser | NLP解析器初始化完成
2025-08-04 02:15:53 | INFO     | src.game.openra_client | OpenRA客户端初始化，服务器: http://localhost:8080
2025-08-04 02:15:53 | INFO     | src.core.agent | Code Alert AI Agent 初始化完成
2025-08-04 02:15:53 | WARNING  | src.game.openra_client | 使用模拟连接模式
2025-08-04 02:15:53 | INFO     | src.game.openra_client | OpenRA服务器连接成功
2025-08-04 02:15:53 | INFO     | src.core.agent | AI Agent 启动成功
2025-08-04 02:15:53 | INFO     | src.core.agent | 处理用户指令: 建造一个电厂
2025-08-04 02:15:53 | INFO     | src.nlp.parser | 解析指令: 建造一个电厂
2025-08-04 02:15:53 | INFO     | src.nlp.parser | 规则匹配成功: build_structure
2025-08-04 02:15:53 | INFO     | src.core.agent | 指令解析结果: build_structure
2025-08-04 02:15:53 | INFO     | src.game.openra_client | 获取游戏状态成功
2025-08-04 02:15:53 | INFO     | src.core.agent | 获取游戏状态成功
2025-08-04 02:15:53 | INFO     | src.core.agent | 建造建筑: power_plant
2025-08-04 02:15:53 | INFO     | src.game.openra_client | 执行游戏指令: {'action': 'build_structure', 'structure_type': 'power_plant', 'position': {'x': 250, 'y': 250}}
2025-08-04 02:15:53 | INFO     | src.game.openra_client | 游戏指令执行成功
2025-08-04 02:15:53 | INFO     | src.core.agent | 处理用户指令: 派遣步兵到东边
2025-08-04 02:15:53 | INFO     | src.nlp.parser | 解析指令: 派遣步兵到东边
2025-08-04 02:15:53 | INFO     | src.nlp.parser | 规则匹配成功: move_unit
2025-08-04 02:15:53 | INFO     | src.core.agent | 指令解析结果: move_unit
2025-08-04 02:15:53 | INFO     | src.game.openra_client | 获取游戏状态成功
2025-08-04 02:15:53 | INFO     | src.core.agent | 获取游戏状态成功
2025-08-04 02:15:53 | INFO     | src.core.agent | 移动单位: infantry 到 东
2025-08-04 02:15:53 | INFO     | src.game.openra_client | 执行游戏指令: {'action': 'move_unit', 'unit_id': 'unit_1', 'target_position': {'x': 400, 'y': 200}}
2025-08-04 02:15:53 | INFO     | src.game.openra_client | 游戏指令执行成功
2025-08-04 02:15:53 | INFO     | src.core.agent | 处理用户指令: 攻击敌人
2025-08-04 02:15:53 | INFO     | src.nlp.parser | 解析指令: 攻击敌人
2025-08-04 02:15:53 | INFO     | src.nlp.parser | 规则匹配成功: attack_target
2025-08-04 02:15:53 | INFO     | src.core.agent | 指令解析结果: attack_target
2025-08-04 02:15:53 | INFO     | src.game.openra_client | 获取游戏状态成功
2025-08-04 02:15:53 | INFO     | src.core.agent | 获取游戏状态成功
2025-08-04 02:15:53 | INFO     | src.core.agent | 攻击目标: enemy
2025-08-04 02:15:53 | INFO     | src.core.agent | 处理用户指令: 探索地图
2025-08-04 02:15:53 | INFO     | src.nlp.parser | 解析指令: 探索地图
2025-08-04 02:15:53 | INFO     | src.nlp.parser | 规则匹配成功: explore_map
2025-08-04 02:15:53 | INFO     | src.core.agent | 指令解析结果: explore_map
2025-08-04 02:15:53 | INFO     | src.game.openra_client | 获取游戏状态成功
2025-08-04 02:15:53 | INFO     | src.core.agent | 获取游戏状态成功
2025-08-04 02:15:53 | INFO     | src.core.agent | 探索地图
2025-08-04 02:15:53 | INFO     | src.game.openra_client | 执行游戏指令: {'action': 'explore_area', 'area': {'x': 0, 'y': 0, 'width': 1024, 'height': 1024}}
2025-08-04 02:15:53 | INFO     | src.game.openra_client | 游戏指令执行成功
2025-08-04 02:15:53 | INFO     | src.game.openra_client | OpenRA服务器连接已断开
2025-08-04 02:15:53 | INFO     | src.core.agent | AI Agent 已停止
2025-08-04 02:16:16 | INFO     | root | 日志系统初始化完成，级别: INFO
2025-08-04 02:16:16 | INFO     | __main__ | 🎮 Code Alert AI Agent 启动
2025-08-04 02:16:16 | INFO     | __main__ | ==================================================
2025-08-04 02:16:16 | INFO     | __main__ | 🏆 目标: Code Alert 黑客松大赛
2025-08-04 02:16:16 | INFO     | __main__ | 🎯 任务: 红警游戏AI智能体
2025-08-04 02:16:16 | INFO     | __main__ | 🚀 技术栈: Python + FastAPI + OpenRA
2025-08-04 02:16:16 | INFO     | __main__ | ==================================================
2025-08-04 02:16:17 | INFO     | root | 日志系统初始化完成，级别: INFO
2025-08-04 02:16:17 | INFO     | src.main | 🚀 Code Alert AI Agent 启动中...
2025-08-04 02:16:17 | INFO     | src.main | 配置信息: Debug=False, LogLevel=INFO
2025-08-04 02:16:42 | INFO     | src.main | 📥 GET http://localhost:8000/
2025-08-04 02:16:42 | INFO     | src.main | 📤 GET http://localhost:8000/ - 200 (0.001s)
2025-08-04 02:16:57 | INFO     | src.main | 📥 GET http://localhost:8000/api/v1/health
2025-08-04 02:16:58 | INFO     | src.nlp.parser | spaCy中文模型加载成功
2025-08-04 02:16:58 | INFO     | src.nlp.parser | NLP解析器初始化完成
2025-08-04 02:16:58 | INFO     | src.game.openra_client | OpenRA客户端初始化，服务器: http://localhost:8080
2025-08-04 02:16:58 | INFO     | src.core.agent | Code Alert AI Agent 初始化完成
2025-08-04 02:16:58 | WARNING  | src.game.openra_client | 使用模拟连接模式
2025-08-04 02:16:58 | INFO     | src.game.openra_client | OpenRA服务器连接成功
2025-08-04 02:16:58 | INFO     | src.core.agent | AI Agent 启动成功
2025-08-04 02:16:58 | INFO     | src.main | 📤 GET http://localhost:8000/api/v1/health - 200 (0.501s)
2025-08-04 02:17:08 | INFO     | src.main | 📥 POST http://localhost:8000/api/v1/command
2025-08-04 02:17:08 | INFO     | src.api.routes | 收到指令请求: 建造一个电厂
2025-08-04 02:17:08 | INFO     | src.core.agent | 处理用户指令: 建造一个电厂
2025-08-04 02:17:08 | INFO     | src.nlp.parser | 解析指令: 建造一个电厂
2025-08-04 02:17:08 | INFO     | src.nlp.parser | 规则匹配成功: build_structure
2025-08-04 02:17:08 | INFO     | src.core.agent | 指令解析结果: build_structure
2025-08-04 02:17:08 | INFO     | src.game.openra_client | 获取游戏状态成功
2025-08-04 02:17:08 | INFO     | src.core.agent | 获取游戏状态成功
2025-08-04 02:17:08 | INFO     | src.core.agent | 建造建筑: power_plant
2025-08-04 02:17:08 | INFO     | src.game.openra_client | 执行游戏指令: {'action': 'build_structure', 'structure_type': 'power_plant', 'position': {'x': 250, 'y': 250}}
2025-08-04 02:17:08 | INFO     | src.game.openra_client | 游戏指令执行成功
2025-08-04 02:17:08 | ERROR    | src.api.routes | 指令执行失败: 1 validation error for CommandResponse
command_type
  Input should be a valid string [type=string_type, input_value=None, input_type=NoneType]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
2025-08-04 02:17:08 | INFO     | src.main | 📤 POST http://localhost:8000/api/v1/command - 500 (0.004s)
2025-08-04 02:18:35 | INFO     | root | 日志系统初始化完成，级别: INFO
2025-08-04 02:18:35 | INFO     | __main__ | 🎮 Code Alert AI Agent 启动
2025-08-04 02:18:35 | INFO     | __main__ | ==================================================
2025-08-04 02:18:35 | INFO     | __main__ | 🏆 目标: Code Alert 黑客松大赛
2025-08-04 02:18:35 | INFO     | __main__ | 🎯 任务: 红警游戏AI智能体
2025-08-04 02:18:35 | INFO     | __main__ | 🚀 技术栈: Python + FastAPI + OpenRA
2025-08-04 02:18:35 | INFO     | __main__ | ==================================================
2025-08-04 02:18:35 | INFO     | root | 日志系统初始化完成，级别: INFO
2025-08-04 02:18:35 | INFO     | src.main | 🚀 Code Alert AI Agent 启动中...
2025-08-04 02:18:35 | INFO     | src.main | 配置信息: Debug=False, LogLevel=INFO
2025-08-04 02:19:25 | INFO     | src.main | 📥 POST http://localhost:8000/api/v1/command
2025-08-04 02:19:25 | INFO     | src.nlp.parser | spaCy中文模型加载成功
2025-08-04 02:19:25 | INFO     | src.nlp.parser | NLP解析器初始化完成
2025-08-04 02:19:25 | INFO     | src.game.openra_client | OpenRA客户端初始化，服务器: http://localhost:8080
2025-08-04 02:19:25 | INFO     | src.core.agent | Code Alert AI Agent 初始化完成
2025-08-04 02:19:25 | WARNING  | src.game.openra_client | 使用模拟连接模式
2025-08-04 02:19:25 | INFO     | src.game.openra_client | OpenRA服务器连接成功
2025-08-04 02:19:25 | INFO     | src.core.agent | AI Agent 启动成功
2025-08-04 02:19:25 | INFO     | src.api.routes | 收到指令请求: 建造一个电厂
2025-08-04 02:19:25 | INFO     | src.core.agent | 处理用户指令: 建造一个电厂
2025-08-04 02:19:25 | INFO     | src.nlp.parser | 解析指令: 建造一个电厂
2025-08-04 02:19:25 | INFO     | src.nlp.parser | 规则匹配成功: build_structure
2025-08-04 02:19:25 | INFO     | src.core.agent | 指令解析结果: build_structure
2025-08-04 02:19:25 | INFO     | src.game.openra_client | 获取游戏状态成功
2025-08-04 02:19:25 | INFO     | src.core.agent | 获取游戏状态成功
2025-08-04 02:19:25 | INFO     | src.core.agent | 建造建筑: power_plant
2025-08-04 02:19:25 | INFO     | src.game.openra_client | 执行游戏指令: {'action': 'build_structure', 'structure_type': 'power_plant', 'position': {'x': 250, 'y': 250}}
2025-08-04 02:19:25 | INFO     | src.game.openra_client | 游戏指令执行成功
2025-08-04 02:19:25 | INFO     | src.main | 📤 POST http://localhost:8000/api/v1/command - 200 (0.493s)
2025-08-04 02:20:35 | INFO     | src.main | 📥 POST http://localhost:8000/api/v1/command
2025-08-04 02:20:35 | INFO     | src.api.routes | 收到指令请求: 建造一个电厂
2025-08-04 02:20:35 | INFO     | src.core.agent | 处理用户指令: 建造一个电厂
2025-08-04 02:20:35 | INFO     | src.nlp.parser | 解析指令: 建造一个电厂
2025-08-04 02:20:35 | INFO     | src.nlp.parser | 规则匹配成功: build_structure
2025-08-04 02:20:35 | INFO     | src.core.agent | 指令解析结果: build_structure
2025-08-04 02:20:35 | INFO     | src.game.openra_client | 获取游戏状态成功
2025-08-04 02:20:35 | INFO     | src.core.agent | 获取游戏状态成功
2025-08-04 02:20:35 | INFO     | src.core.agent | 建造建筑: power_plant
2025-08-04 02:20:35 | INFO     | src.game.openra_client | 执行游戏指令: {'action': 'build_structure', 'structure_type': 'power_plant', 'position': {'x': 250, 'y': 250}}
2025-08-04 02:20:35 | INFO     | src.game.openra_client | 游戏指令执行成功
2025-08-04 02:20:35 | INFO     | src.main | 📤 POST http://localhost:8000/api/v1/command - 200 (0.002s)
2025-08-04 02:21:21 | INFO     | src.main | 📥 POST http://localhost:8000/api/v1/command
2025-08-04 02:21:21 | INFO     | src.api.routes | 收到指令请求: 攻击敌人
2025-08-04 02:21:21 | INFO     | src.core.agent | 处理用户指令: 攻击敌人
2025-08-04 02:21:21 | INFO     | src.nlp.parser | 解析指令: 攻击敌人
2025-08-04 02:21:21 | INFO     | src.nlp.parser | 规则匹配成功: attack_target
2025-08-04 02:21:21 | INFO     | src.core.agent | 指令解析结果: attack_target
2025-08-04 02:21:21 | INFO     | src.game.openra_client | 获取游戏状态成功
2025-08-04 02:21:21 | INFO     | src.core.agent | 获取游戏状态成功
2025-08-04 02:21:21 | INFO     | src.core.agent | 攻击目标: enemy
2025-08-04 02:21:21 | INFO     | src.main | 📤 POST http://localhost:8000/api/v1/command - 200 (0.002s)
2025-08-04 03:26:55 | INFO     | root | 日志系统初始化完成，级别: INFO
2025-08-04 03:26:55 | INFO     | src.nlp.local_llm | 本地LLM客户端初始化，设备: mps
2025-08-04 03:26:55 | INFO     | src.nlp.local_llm | 使用轻量级本地NLP方案
2025-08-04 03:26:55 | INFO     | src.nlp.local_llm | 模型已卸载
2025-08-04 03:26:55 | INFO     | src.game.openra_integration | 找到OpenRA安装: /Applications/OpenRA - Red Alert.app
2025-08-04 03:26:55 | INFO     | src.game.openra_integration | OpenRA Lua集成初始化，OpenRA路径: /Applications/OpenRA - Red Alert.app
2025-08-04 03:26:55 | INFO     | src.game.openra_integration | 创建基础Lua脚本: data/openra_scripts/ai_agent.lua
2025-08-04 03:26:55 | INFO     | src.game.openra_integration | OpenRA Lua集成连接成功
2025-08-04 03:26:56 | INFO     | src.game.openra_integration | OpenRA Lua集成已断开
2025-08-04 03:26:56 | INFO     | src.nlp.parser | spaCy中文模型加载成功
2025-08-04 03:26:56 | INFO     | src.nlp.parser | 本地LLM客户端加载成功
2025-08-04 03:26:56 | INFO     | src.nlp.parser | NLP解析器初始化完成，使用本地LLM: True
2025-08-04 03:26:56 | INFO     | src.nlp.parser | 解析指令: 建造一个电厂
2025-08-04 03:26:56 | INFO     | src.nlp.parser | 规则匹配成功: build_structure
2025-08-04 03:26:56 | INFO     | src.nlp.parser | 解析指令: 派遣步兵到A点探索
2025-08-04 03:26:56 | INFO     | src.nlp.parser | 规则匹配成功: move_unit
2025-08-04 03:26:56 | INFO     | src.nlp.parser | 解析指令: 攻击敌方基地
2025-08-04 03:26:56 | INFO     | src.nlp.parser | 规则匹配成功: attack_target
2025-08-04 03:26:56 | INFO     | src.nlp.parser | 解析指令: 探索整个地图
2025-08-04 03:26:56 | INFO     | src.nlp.parser | 规则匹配成功: explore_map
2025-08-04 03:26:56 | INFO     | src.nlp.parser | 解析指令: 防守我们的基地
2025-08-04 03:26:56 | INFO     | src.nlp.parser | 规则匹配成功: defend_base
2025-08-04 03:26:56 | INFO     | src.nlp.parser | 解析指令: 撤退到安全区域
2025-08-04 03:26:56 | INFO     | src.nlp.parser | 规则匹配成功: retreat
2025-08-04 03:26:56 | INFO     | src.nlp.parser | 解析指令: 收集更多资源
2025-08-04 03:26:56 | INFO     | src.nlp.parser | 规则匹配成功: collect_resources
2025-08-04 03:26:56 | INFO     | src.nlp.parser | 解析指令: 修复受损的建筑
2025-08-04 03:26:56 | INFO     | src.nlp.parser | 规则匹配成功: repair
2025-08-04 03:26:56 | INFO     | src.nlp.parser | 解析指令: 让所有单位集合
2025-08-04 03:26:56 | INFO     | src.nlp.local_llm | 使用轻量级本地NLP方案
2025-08-04 03:26:56 | WARNING  | src.nlp.parser | 无法识别指令: 让所有单位集合
2025-08-04 03:26:56 | INFO     | src.nlp.parser | 解析指令: 这是一个无法识别的复杂指令
2025-08-04 03:26:56 | WARNING  | src.nlp.parser | 无法识别指令: 这是一个无法识别的复杂指令
2025-08-04 03:26:57 | INFO     | src.nlp.parser | spaCy中文模型加载成功
2025-08-04 03:26:57 | INFO     | src.nlp.parser | 本地LLM客户端加载成功
2025-08-04 03:26:57 | INFO     | src.nlp.parser | NLP解析器初始化完成，使用本地LLM: True
2025-08-04 03:26:57 | INFO     | src.game.openra_client | OpenRA客户端初始化，服务器: http://localhost:8080, 真实集成: True
2025-08-04 03:26:57 | INFO     | src.core.agent | Code Alert AI Agent 初始化完成
2025-08-04 03:26:57 | INFO     | src.game.openra_integration | 创建基础Lua脚本: data/openra_scripts/ai_agent.lua
2025-08-04 03:26:57 | INFO     | src.game.openra_integration | OpenRA Lua集成连接成功
2025-08-04 03:26:57 | INFO     | src.game.openra_client | OpenRA Lua集成连接成功
2025-08-04 03:26:57 | INFO     | src.core.agent | AI Agent 启动成功
2025-08-04 03:26:57 | INFO     | src.core.agent | 处理用户指令: 建造一个电厂
2025-08-04 03:26:57 | INFO     | src.nlp.parser | 解析指令: 建造一个电厂
2025-08-04 03:26:57 | INFO     | src.nlp.parser | 规则匹配成功: build_structure
2025-08-04 03:26:57 | INFO     | src.core.agent | 指令解析结果: build_structure
2025-08-04 03:26:57 | INFO     | src.core.agent | 获取游戏状态成功
2025-08-04 03:26:57 | INFO     | src.core.agent | 建造建筑: power_plant
2025-08-04 03:26:57 | INFO     | src.core.agent | 处理用户指令: 派遣步兵到东边探索
2025-08-04 03:26:57 | INFO     | src.nlp.parser | 解析指令: 派遣步兵到东边探索
2025-08-04 03:26:57 | INFO     | src.nlp.parser | 规则匹配成功: move_unit
2025-08-04 03:26:57 | INFO     | src.core.agent | 指令解析结果: move_unit
2025-08-04 03:26:57 | INFO     | src.core.agent | 获取游戏状态成功
2025-08-04 03:26:57 | INFO     | src.core.agent | 移动单位: infantry 到 东
2025-08-04 03:26:57 | INFO     | src.core.agent | 处理用户指令: 攻击敌方单位
2025-08-04 03:26:57 | INFO     | src.nlp.parser | 解析指令: 攻击敌方单位
2025-08-04 03:26:57 | INFO     | src.nlp.parser | 规则匹配成功: attack_target
2025-08-04 03:26:57 | INFO     | src.core.agent | 指令解析结果: attack_target
2025-08-04 03:26:57 | INFO     | src.core.agent | 获取游戏状态成功
2025-08-04 03:26:57 | INFO     | src.core.agent | 攻击目标: enemy
2025-08-04 03:26:57 | INFO     | src.core.agent | 处理用户指令: 探索地图找到敌人
2025-08-04 03:26:57 | INFO     | src.nlp.parser | 解析指令: 探索地图找到敌人
2025-08-04 03:26:57 | INFO     | src.nlp.parser | 规则匹配成功: explore_map
2025-08-04 03:26:57 | INFO     | src.core.agent | 指令解析结果: explore_map
2025-08-04 03:26:57 | INFO     | src.core.agent | 获取游戏状态成功
2025-08-04 03:26:57 | INFO     | src.core.agent | 探索地图
2025-08-04 03:26:57 | INFO     | src.game.openra_client | 执行游戏指令: {'action': 'explore_area', 'area': {'x': 0, 'y': 0, 'width': 128, 'height': 128}}
2025-08-04 03:26:57 | INFO     | src.core.agent | 处理用户指令: 防守基地
2025-08-04 03:26:57 | INFO     | src.nlp.parser | 解析指令: 防守基地
2025-08-04 03:26:57 | INFO     | src.nlp.parser | 规则匹配成功: defend_base
2025-08-04 03:26:57 | INFO     | src.core.agent | 指令解析结果: defend_base
2025-08-04 03:26:57 | INFO     | src.core.agent | 获取游戏状态成功
2025-08-04 03:26:57 | INFO     | src.core.agent | 防守基地
2025-08-04 03:26:57 | INFO     | src.game.openra_client | 执行游戏指令: {'action': 'defend_base', 'strategy': 'defend_base'}
2025-08-04 03:26:57 | INFO     | src.core.agent | 处理用户指令: 收集资源
2025-08-04 03:26:57 | INFO     | src.nlp.parser | 解析指令: 收集资源
2025-08-04 03:26:57 | INFO     | src.nlp.parser | 规则匹配成功: collect_resources
2025-08-04 03:26:57 | INFO     | src.core.agent | 指令解析结果: collect_resources
2025-08-04 03:26:57 | INFO     | src.core.agent | 获取游戏状态成功
2025-08-04 03:26:57 | INFO     | src.core.agent | 收集资源
2025-08-04 03:26:57 | INFO     | src.game.openra_client | 执行游戏指令: {'action': 'collect_resources'}
2025-08-04 03:26:57 | INFO     | src.game.openra_client | OpenRA服务器连接已断开
2025-08-04 03:26:57 | INFO     | src.core.agent | AI Agent 已停止
2025-08-04 03:26:58 | INFO     | src.nlp.parser | spaCy中文模型加载成功
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 本地LLM客户端加载成功
2025-08-04 03:26:58 | INFO     | src.nlp.parser | NLP解析器初始化完成，使用本地LLM: True
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 解析指令: 建造电厂
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 规则匹配成功: build_structure
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 解析指令: 攻击敌人
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 规则匹配成功: attack_target
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 解析指令: 探索地图
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 规则匹配成功: explore_map
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 解析指令: 防守基地
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 规则匹配成功: defend_base
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 解析指令: 收集资源
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 规则匹配成功: collect_resources
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 解析指令: 建造电厂
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 规则匹配成功: build_structure
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 解析指令: 攻击敌人
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 规则匹配成功: attack_target
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 解析指令: 探索地图
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 规则匹配成功: explore_map
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 解析指令: 防守基地
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 规则匹配成功: defend_base
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 解析指令: 收集资源
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 规则匹配成功: collect_resources
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 解析指令: 建造电厂
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 规则匹配成功: build_structure
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 解析指令: 攻击敌人
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 规则匹配成功: attack_target
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 解析指令: 探索地图
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 规则匹配成功: explore_map
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 解析指令: 防守基地
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 规则匹配成功: defend_base
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 解析指令: 收集资源
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 规则匹配成功: collect_resources
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 解析指令: 建造电厂
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 规则匹配成功: build_structure
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 解析指令: 攻击敌人
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 规则匹配成功: attack_target
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 解析指令: 探索地图
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 规则匹配成功: explore_map
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 解析指令: 防守基地
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 规则匹配成功: defend_base
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 解析指令: 收集资源
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 规则匹配成功: collect_resources
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 解析指令: 建造电厂
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 规则匹配成功: build_structure
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 解析指令: 攻击敌人
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 规则匹配成功: attack_target
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 解析指令: 探索地图
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 规则匹配成功: explore_map
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 解析指令: 防守基地
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 规则匹配成功: defend_base
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 解析指令: 收集资源
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 规则匹配成功: collect_resources
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 解析指令: 建造电厂
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 规则匹配成功: build_structure
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 解析指令: 攻击敌人
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 规则匹配成功: attack_target
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 解析指令: 探索地图
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 规则匹配成功: explore_map
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 解析指令: 防守基地
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 规则匹配成功: defend_base
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 解析指令: 收集资源
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 规则匹配成功: collect_resources
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 解析指令: 建造电厂
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 规则匹配成功: build_structure
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 解析指令: 攻击敌人
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 规则匹配成功: attack_target
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 解析指令: 探索地图
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 规则匹配成功: explore_map
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 解析指令: 防守基地
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 规则匹配成功: defend_base
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 解析指令: 收集资源
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 规则匹配成功: collect_resources
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 解析指令: 建造电厂
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 规则匹配成功: build_structure
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 解析指令: 攻击敌人
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 规则匹配成功: attack_target
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 解析指令: 探索地图
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 规则匹配成功: explore_map
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 解析指令: 防守基地
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 规则匹配成功: defend_base
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 解析指令: 收集资源
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 规则匹配成功: collect_resources
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 解析指令: 建造电厂
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 规则匹配成功: build_structure
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 解析指令: 攻击敌人
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 规则匹配成功: attack_target
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 解析指令: 探索地图
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 规则匹配成功: explore_map
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 解析指令: 防守基地
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 规则匹配成功: defend_base
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 解析指令: 收集资源
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 规则匹配成功: collect_resources
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 解析指令: 建造电厂
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 规则匹配成功: build_structure
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 解析指令: 攻击敌人
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 规则匹配成功: attack_target
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 解析指令: 探索地图
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 规则匹配成功: explore_map
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 解析指令: 防守基地
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 规则匹配成功: defend_base
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 解析指令: 收集资源
2025-08-04 03:26:58 | INFO     | src.nlp.parser | 规则匹配成功: collect_resources
2025-08-04 03:28:59 | INFO     | root | 日志系统初始化完成，级别: INFO
2025-08-04 03:28:59 | INFO     | __main__ | 初始化关卡: 基础建设训练
2025-08-04 03:28:59 | INFO     | __main__ | 🎮 开始关卡: 基础建设训练
2025-08-04 03:28:59 | INFO     | __main__ | 📋 关卡描述: 学习基本的建造指令，建设一个基础的红警基地
2025-08-04 03:28:59 | INFO     | src.nlp.parser | spaCy中文模型加载成功
2025-08-04 03:29:01 | INFO     | src.nlp.local_llm | 本地LLM客户端初始化，设备: mps
2025-08-04 03:29:01 | INFO     | src.nlp.parser | 本地LLM客户端加载成功
2025-08-04 03:29:01 | INFO     | src.nlp.parser | NLP解析器初始化完成，使用本地LLM: True
2025-08-04 03:29:01 | INFO     | src.game.openra_integration | 找到OpenRA安装: /Applications/OpenRA - Red Alert.app
2025-08-04 03:29:01 | INFO     | src.game.openra_integration | OpenRA Lua集成初始化，OpenRA路径: /Applications/OpenRA - Red Alert.app
2025-08-04 03:29:01 | INFO     | src.game.openra_client | OpenRA客户端初始化，服务器: http://localhost:8080, 真实集成: True
2025-08-04 03:29:01 | INFO     | src.core.agent | Code Alert AI Agent 初始化完成
2025-08-04 03:29:01 | INFO     | src.game.openra_integration | 创建基础Lua脚本: data/openra_scripts/ai_agent.lua
2025-08-04 03:29:01 | INFO     | src.game.openra_integration | OpenRA Lua集成连接成功
2025-08-04 03:29:01 | INFO     | src.game.openra_client | OpenRA Lua集成连接成功
2025-08-04 03:29:01 | INFO     | src.core.agent | AI Agent 启动成功
2025-08-04 03:29:01 | INFO     | __main__ | 🎯 关卡目标:
2025-08-04 03:29:01 | INFO     | __main__ |   1. 建造一个电厂 (必须)
2025-08-04 03:29:01 | INFO     | __main__ |   2. 建造一个兵营 (必须)
2025-08-04 03:29:01 | INFO     | __main__ |   3. 训练步兵单位 (必须)
2025-08-04 03:29:01 | INFO     | __main__ |   4. 建造防御设施 (可选)
2025-08-04 03:29:01 | INFO     | __main__ |   5. 探索周围区域 (可选)
2025-08-04 03:29:01 | INFO     | __main__ | 初始资源: {'cash': 2000, 'power': 100}
2025-08-04 03:29:01 | INFO     | __main__ | 🚀 开始执行关卡任务
2025-08-04 03:29:01 | INFO     | __main__ | 📝 执行任务: 建造一个电厂
2025-08-04 03:29:01 | INFO     | src.core.agent | 处理用户指令: 建造一个电厂
2025-08-04 03:29:01 | INFO     | src.nlp.parser | 解析指令: 建造一个电厂
2025-08-04 03:29:01 | INFO     | src.nlp.parser | 规则匹配成功: build_structure
2025-08-04 03:29:01 | INFO     | src.core.agent | 指令解析结果: build_structure
2025-08-04 03:29:01 | INFO     | src.core.agent | 获取游戏状态成功
2025-08-04 03:29:01 | INFO     | src.core.agent | 建造建筑: power_plant
2025-08-04 03:29:01 | INFO     | __main__ | ✅ 任务成功: 指令执行成功: build_structure
2025-08-04 03:29:02 | INFO     | __main__ | 💰 当前资源: {'cash': 2000, 'power': 100}
2025-08-04 03:29:02 | INFO     | __main__ | 📝 执行任务: 建造一个兵营
2025-08-04 03:29:02 | INFO     | src.core.agent | 处理用户指令: 建造一个兵营
2025-08-04 03:29:02 | INFO     | src.nlp.parser | 解析指令: 建造一个兵营
2025-08-04 03:29:02 | INFO     | src.nlp.parser | 规则匹配成功: build_structure
2025-08-04 03:29:02 | INFO     | src.core.agent | 指令解析结果: build_structure
2025-08-04 03:29:02 | INFO     | src.core.agent | 获取游戏状态成功
2025-08-04 03:29:02 | INFO     | src.core.agent | 建造建筑: power_plant
2025-08-04 03:29:02 | INFO     | __main__ | ✅ 任务成功: 指令执行成功: build_structure
2025-08-04 03:29:03 | INFO     | __main__ | 📝 执行任务: 训练步兵
2025-08-04 03:29:03 | INFO     | src.core.agent | 处理用户指令: 训练步兵
2025-08-04 03:29:03 | INFO     | src.nlp.parser | 解析指令: 训练步兵
2025-08-04 03:29:03 | INFO     | src.nlp.local_llm | 使用轻量级本地NLP方案
2025-08-04 03:29:03 | WARNING  | src.nlp.parser | 无法识别指令: 训练步兵
2025-08-04 03:29:03 | INFO     | src.core.agent | 指令解析结果: unknown
2025-08-04 03:29:03 | INFO     | src.core.agent | 获取游戏状态成功
2025-08-04 03:29:03 | INFO     | __main__ | ✅ 任务成功: 指令执行成功: unknown
2025-08-04 03:29:04 | INFO     | __main__ | 📝 执行任务: 建造防御塔
2025-08-04 03:29:04 | INFO     | src.core.agent | 处理用户指令: 建造防御塔
2025-08-04 03:29:04 | INFO     | src.nlp.parser | 解析指令: 建造防御塔
2025-08-04 03:29:04 | INFO     | src.nlp.parser | 规则匹配成功: build_structure
2025-08-04 03:29:04 | INFO     | src.core.agent | 指令解析结果: build_structure
2025-08-04 03:29:04 | INFO     | src.core.agent | 获取游戏状态成功
2025-08-04 03:29:04 | INFO     | src.core.agent | 建造建筑: power_plant
2025-08-04 03:29:04 | INFO     | __main__ | ✅ 任务成功: 指令执行成功: build_structure
2025-08-04 03:29:05 | INFO     | __main__ | 📝 执行任务: 探索地图
2025-08-04 03:29:05 | INFO     | src.core.agent | 处理用户指令: 探索地图
2025-08-04 03:29:05 | INFO     | src.nlp.parser | 解析指令: 探索地图
2025-08-04 03:29:05 | INFO     | src.nlp.parser | 规则匹配成功: explore_map
2025-08-04 03:29:05 | INFO     | src.core.agent | 指令解析结果: explore_map
2025-08-04 03:29:05 | INFO     | src.core.agent | 获取游戏状态成功
2025-08-04 03:29:05 | INFO     | src.core.agent | 探索地图
2025-08-04 03:29:05 | INFO     | src.game.openra_client | 执行游戏指令: {'action': 'explore_area', 'area': {'x': 0, 'y': 0, 'width': 128, 'height': 128}}
2025-08-04 03:29:06 | INFO     | __main__ | ✅ 任务成功: 指令执行成功: explore_map
2025-08-04 03:29:07 | INFO     | __main__ | ✅ 关卡任务执行完成
2025-08-04 03:29:07 | INFO     | __main__ | 🏆 关卡结果
2025-08-04 03:29:07 | INFO     | __main__ | ==================================================
2025-08-04 03:29:07 | INFO     | __main__ | 关卡状态: ✅ 通过
2025-08-04 03:29:07 | INFO     | __main__ | 最终分数: 123
2025-08-04 03:29:07 | INFO     | __main__ | 完成目标: 5/5
2025-08-04 03:29:07 | INFO     | __main__ | 执行时间: 7.50秒
2025-08-04 03:29:07 | INFO     | __main__ | 使用指令: 5个
2025-08-04 03:29:07 | INFO     | __main__ | 
📋 目标完成情况:
2025-08-04 03:29:07 | INFO     | __main__ |   ✅ 建造一个电厂 (必须)
2025-08-04 03:29:07 | INFO     | __main__ |   ✅ 建造一个兵营 (必须)
2025-08-04 03:29:07 | INFO     | __main__ |   ✅ 训练步兵单位 (必须)
2025-08-04 03:29:07 | INFO     | __main__ |   ✅ 建造防御设施 (可选)
2025-08-04 03:29:07 | INFO     | __main__ |   ✅ 探索周围区域 (可选)
2025-08-04 03:29:07 | INFO     | __main__ | 
📝 使用的指令:
2025-08-04 03:29:07 | INFO     | __main__ |   1. 建造一个电厂
2025-08-04 03:29:07 | INFO     | __main__ |   2. 建造一个兵营
2025-08-04 03:29:07 | INFO     | __main__ |   3. 训练步兵
2025-08-04 03:29:07 | INFO     | __main__ |   4. 建造防御塔
2025-08-04 03:29:07 | INFO     | __main__ |   5. 探索地图
2025-08-04 03:29:07 | INFO     | src.game.openra_client | OpenRA服务器连接已断开
2025-08-04 03:29:07 | INFO     | src.core.agent | AI Agent 已停止
