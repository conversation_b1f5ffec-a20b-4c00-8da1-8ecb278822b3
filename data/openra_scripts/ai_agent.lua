
-- Code Alert AI Agent <PERSON><PERSON>
-- 基础游戏控制脚本

-- 全局变量
local aiAgent = {}
local gameState = {}
local commandQueue = {}

-- 初始化函数
function WorldLoaded()
    Media.Debug("Code Alert AI Agent 已加载")
    
    -- 获取玩家
    aiAgent.player = Player.GetPlayer("Multi0") or Player.GetPlayer("GoodGuy")
    if not aiAgent.player then
        Media.Debug("错误: 无法找到AI玩家")
        return
    end
    
    Media.Debug("AI玩家: " .. aiAgent.player.Name)
    
    -- 设置初始目标
    if aiAgent.player.HasPrerequisites({"proc"}) then
        Media.Debug("玩家有基础建筑")
    end
    
    -- 开始主循环
    Trigger.AfterDelay(25, MainLoop)
end

-- 主循环
function MainLoop()
    UpdateGameState()
    ProcessCommands()
    
    -- 每秒执行一次
    Trigger.AfterDelay(25, MainLoop)
end

-- 更新游戏状态
function UpdateGameState()
    if not aiAgent.player then return end
    
    gameState.units = aiAgent.player.GetActors()
    gameState.buildings = {}
    gameState.resources = aiAgent.player.Resources
    gameState.cash = aiAgent.player.Cash
    
    -- 分类单位和建筑
    for _, actor in pairs(gameState.units) do
        if actor.HasProperty("Building") then
            table.insert(gameState.buildings, actor)
        end
    end
end

-- 处理指令队列
function ProcessCommands()
    -- 这里会处理从外部传入的指令
    -- 实际实现中会通过文件或其他方式接收指令
end

-- 建造建筑
function BuildStructure(structureType, position)
    if not aiAgent.player then return false end
    
    local builders = aiAgent.player.GetActorsByType("mcv")
    if #builders == 0 then
        builders = aiAgent.player.GetActorsByType("fact")
    end
    
    if #builders > 0 then
        local builder = builders[1]
        if builder.Build({structureType}) then
            Media.Debug("开始建造: " .. structureType)
            return true
        end
    end
    
    Media.Debug("无法建造: " .. structureType)
    return false
end

-- 移动单位
function MoveUnit(unitType, targetPosition)
    if not aiAgent.player then return false end
    
    local units = aiAgent.player.GetActorsByType(unitType)
    if #units > 0 then
        local unit = units[1]
        unit.Move(targetPosition)
        Media.Debug("移动单位: " .. unitType .. " 到 " .. targetPosition.X .. "," .. targetPosition.Y)
        return true
    end
    
    Media.Debug("未找到单位: " .. unitType)
    return false
end

-- 攻击目标
function AttackTarget(unitType, targetType)
    if not aiAgent.player then return false end
    
    local attackers = aiAgent.player.GetActorsByType(unitType)
    if #attackers == 0 then
        attackers = aiAgent.player.GetGroundAttackers()
    end
    
    if #attackers > 0 then
        local attacker = attackers[1]
        
        -- 查找敌方目标
        local enemies = Map.ActorsInWorld
        for _, enemy in pairs(enemies) do
            if enemy.Owner ~= aiAgent.player and enemy.Type == targetType then
                attacker.Attack(enemy)
                Media.Debug("攻击目标: " .. targetType)
                return true
            end
        end
    end
    
    Media.Debug("无法攻击目标: " .. targetType)
    return false
end

-- 探索地图
function ExploreMap()
    if not aiAgent.player then return false end
    
    local scouts = aiAgent.player.GetActorsByType("e1")
    if #scouts > 0 then
        local scout = scouts[1]
        local randomCell = Map.RandomCell()
        scout.Move(randomCell)
        Media.Debug("派遣侦察兵探索")
        return true
    end
    
    Media.Debug("没有可用的侦察单位")
    return false
end

-- 防守基地
function DefendBase()
    if not aiAgent.player then return false end
    
    local defenders = aiAgent.player.GetGroundAttackers()
    if #defenders > 0 then
        for _, defender in pairs(defenders) do
            defender.Guard(aiAgent.player.GetActorsByType("fact")[1] or aiAgent.player.GetActors()[1])
        end
        Media.Debug("设置基地防守")
        return true
    end
    
    Media.Debug("没有可用的防守单位")
    return false
end

-- 导出函数供外部调用
aiAgent.BuildStructure = BuildStructure
aiAgent.MoveUnit = MoveUnit
aiAgent.AttackTarget = AttackTarget
aiAgent.ExploreMap = ExploreMap
aiAgent.DefendBase = DefendBase
